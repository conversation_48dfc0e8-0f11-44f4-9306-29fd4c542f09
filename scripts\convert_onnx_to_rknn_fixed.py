#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版ONNX到RKNN转换脚本
解决RGB/BGR通道顺序问题，确保与ONNX模型一致
"""

import os
import sys
import argparse
from pathlib import Path

def check_rknn_toolkit():
    """检查RKNN-Toolkit是否安装"""
    try:
        from rknn.api import RKNN
        print("✅ RKNN-Toolkit 已安装")
        return True
    except ImportError:
        print("❌ RKNN-Toolkit 未安装")
        print("请安装RKNN-Toolkit:")
        print("pip install rknn-toolkit2")
        return False

def convert_onnx_to_rknn_fixed(onnx_path, output_path, target_platform='rk3588', 
                              use_quantization=False, verbose=True):
    """
    修复版ONNX到RKNN转换
    
    Args:
        onnx_path: ONNX模型路径
        output_path: 输出RKNN模型路径
        target_platform: 目标平台
        use_quantization: 是否使用量化
        verbose: 是否显示详细信息
    """
    from rknn.api import RKNN
    
    # 创建RKNN对象
    rknn = RKNN(verbose=verbose)
    
    try:
        print(f"🔧 开始转换ONNX模型: {onnx_path}")
        print(f"🎯 目标平台: {target_platform}")
        print(f"📊 量化模式: {'启用' if use_quantization else '禁用 (FP16精度)'}")
        print(f"🌈 通道顺序: RGB (与ONNX训练时一致)")
        
        # 配置RKNN参数 - 关键修复点
        print("⚙️  配置RKNN参数...")
        config_params = {
            'mean_values': [[0, 0, 0]],           # RGB均值
            'std_values': [[255, 255, 255]],      # RGB标准差
            'target_platform': target_platform,   # 目标平台
        }
        
        # 如果使用量化，添加量化参数
        if use_quantization:
            config_params.update({
                'quantized_dtype': 'asymmetric_quantized-u8',
                'optimization_level': 3,
                'output_optimize': 1,
                'compress_weight': True,
            })
        
        ret = rknn.config(**config_params)
        
        if ret != 0:
            print("❌ RKNN配置失败")
            return False
        
        # 加载ONNX模型
        print("📦 加载ONNX模型...")
        ret = rknn.load_onnx(model=onnx_path)
        if ret != 0:
            print("❌ 加载ONNX模型失败")
            return False
        
        print("✅ ONNX模型加载成功")
        
        # 构建RKNN模型
        if use_quantization:
            print("🔧 构建RKNN模型 (量化模式)...")
            print("⚠️  注意：量化模式需要量化数据集，建议使用FP16模式")
            # 这里需要量化数据集，暂时跳过
            print("❌ 量化模式需要量化数据集，请使用FP16模式")
            return False
        else:
            print("🔧 构建RKNN模型 (FP16精度)...")
            ret = rknn.build(
                do_quantization=False,    # 不进行量化
                rknn_batch_size=1,        # 批次大小
            )
        
        if ret != 0:
            print("❌ 构建RKNN模型失败")
            return False
        
        print("✅ RKNN模型构建成功")
        
        # 导出RKNN模型
        print("💾 导出RKNN模型...")
        ret = rknn.export_rknn(output_path)
        if ret != 0:
            print("❌ 导出RKNN模型失败")
            return False
        
        print("✅ RKNN模型导出成功")
        
        # 获取模型信息
        file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"📊 模型大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 释放资源
        rknn.release()

def main():
    parser = argparse.ArgumentParser(description='修复版ONNX到RKNN转换脚本')
    parser.add_argument('--onnx', type=str, required=True, help='ONNX模型路径')
    parser.add_argument('--output', type=str, help='输出RKNN模型路径')
    parser.add_argument('--target', type=str, default='rk3588',
                       choices=['rk3588', 'rk3566', 'rk3568', 'rk3562'],
                       help='目标平台')
    parser.add_argument('--quantize', action='store_true', 
                       help='启用量化 (需要量化数据集)')
    parser.add_argument('--verbose', action='store_true', default=True,
                       help='显示详细信息')
    
    args = parser.parse_args()
    
    # 检查RKNN-Toolkit
    if not check_rknn_toolkit():
        return
    
    # 检查输入文件
    if not os.path.exists(args.onnx):
        print(f"❌ ONNX文件不存在: {args.onnx}")
        return
    
    # 确定输出路径
    if args.output:
        output_path = args.output
    else:
        onnx_path = Path(args.onnx)
        suffix = "_quantized" if args.quantize else "_fp16_fixed"
        output_path = onnx_path.parent / f"{onnx_path.stem}_{args.target}{suffix}.rknn"
    
    # 创建输出目录
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    print(f"📁 输入文件: {args.onnx}")
    print(f"📁 输出文件: {output_path}")
    print(f"🎯 目标平台: {args.target}")
    print(f"📊 量化模式: {'启用' if args.quantize else '禁用'}")
    print("="*80)
    
    # 执行转换
    success = convert_onnx_to_rknn_fixed(
        onnx_path=args.onnx,
        output_path=output_path,
        target_platform=args.target,
        use_quantization=args.quantize,
        verbose=args.verbose
    )
    
    if success:
        print("\n" + "="*80)
        print("🎉 转换完成!")
        print(f"📁 RKNN模型已保存到: {output_path}")
        print("\n💡 使用建议:")
        print("1. 使用修复后的模型进行推理测试")
        print("2. 确保推理时使用RGB通道顺序")
        print("3. 如果精度仍有问题，检查预处理流程")
    else:
        print("❌ 转换失败")

if __name__ == '__main__':
    main()
