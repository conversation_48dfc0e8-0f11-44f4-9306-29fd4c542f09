#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO模型转ONNX脚本
支持YOLOv11模型转换为ONNX格式，为RKNN量化做准备
"""

import os
import sys
import torch
import argparse
from pathlib import Path
from ultralytics import YOLO
import onnx
import onnxruntime as ort
import numpy as np
import cv2

def convert_to_onnx(model_path, output_path, img_size=640, batch_size=1, device='cpu'):
    """
    将YOLO模型转换为ONNX格式
    
    Args:
        model_path: 输入模型路径
        output_path: 输出ONNX路径
        img_size: 输入图像尺寸
        batch_size: 批次大小
        device: 设备类型
    """
    print(f"🚀 开始转换YOLO模型到ONNX格式")
    print(f"输入模型: {model_path}")
    print(f"输出路径: {output_path}")
    print(f"图像尺寸: {img_size}")
    print(f"批次大小: {batch_size}")
    print(f"设备: {device}")
    
    # 加载YOLO模型
    model = YOLO(model_path)
    
    # 导出为ONNX
    success = model.export(
        format='onnx',
        imgsz=img_size,
        batch=batch_size,
        device=device,
        dynamic=False,  # 固定输入尺寸，便于RKNN量化
        simplify=True,  # 简化模型
        opset=11,       # ONNX opset版本，RKNN 1.6.0推荐使用11
    )
    
    if success:
        # 获取导出的ONNX文件路径
        onnx_path = str(model_path).replace('.pt', '.onnx')
        
        # 移动到指定输出路径
        if onnx_path != output_path:
            import shutil
            shutil.move(onnx_path, output_path)
        
        print(f"✅ ONNX转换成功: {output_path}")
        
        # 验证ONNX模型
        verify_onnx_model(output_path, img_size)
        
        return output_path
    else:
        print("❌ ONNX转换失败")
        return None

def verify_onnx_model(onnx_path, img_size):
    """验证ONNX模型的正确性"""
    print(f"🔍 验证ONNX模型: {onnx_path}")
    
    try:
        # 加载ONNX模型
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)
        print("✅ ONNX模型结构验证通过")
        
        # 创建推理会话
        session = ort.InferenceSession(onnx_path)
        
        # 获取输入输出信息
        input_info = session.get_inputs()[0]
        output_info = session.get_outputs()
        
        print(f"📊 模型信息:")
        print(f"  输入名称: {input_info.name}")
        print(f"  输入形状: {input_info.shape}")
        print(f"  输入类型: {input_info.type}")
        
        for i, output in enumerate(output_info):
            print(f"  输出{i+1}名称: {output.name}")
            print(f"  输出{i+1}形状: {output.shape}")
            print(f"  输出{i+1}类型: {output.type}")
        
        # 测试推理
        test_input = np.random.randn(1, 3, img_size, img_size).astype(np.float32)
        outputs = session.run(None, {input_info.name: test_input})
        
        print(f"✅ ONNX模型推理测试通过")
        print(f"  输出数量: {len(outputs)}")
        for i, output in enumerate(outputs):
            print(f"  输出{i+1}形状: {output.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ ONNX模型验证失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description='Convert YOLO model to ONNX')
    parser.add_argument('--model', type=str, required=True, help='Path to YOLO model (.pt)')
    parser.add_argument('--output', type=str, help='Output ONNX path')
    parser.add_argument('--img-size', type=int, default=640, help='Input image size')
    parser.add_argument('--batch-size', type=int, default=1, help='Batch size')
    parser.add_argument('--device', type=str, default='cpu', help='Device (cpu/cuda)')
    
    args = parser.parse_args()
    
    # 检查输入模型是否存在
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    # 设置输出路径
    if args.output is None:
        args.output = args.model.replace('.pt', '.onnx')
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # 转换模型
    result = convert_to_onnx(
        model_path=args.model,
        output_path=args.output,
        img_size=args.img_size,
        batch_size=args.batch_size,
        device=args.device
    )
    
    if result:
        print(f"🎉 转换完成! ONNX模型保存在: {result}")
    else:
        print("💥 转换失败!")

if __name__ == '__main__':
    main()
