# 实时推理脚本使用说明

## 🚀 科技感ONNX实时推理

### 功能特点
- ✨ **科技感UI设计**：四角边线框 + 淡色蒙版
- 📊 **实时性能监控**：FPS显示、推理时间统计
- 🎯 **高精度检测**：基于YOLOv11模型
- 📹 **视频录制**：支持实时录制检测结果
- ⌨️ **交互控制**：快捷键操作

### 视觉效果
- **检测框**：四角科技感边线框
- **蒙版**：30%透明度彩色覆盖
- **标签**：半透明背景 + 边框
- **FPS显示**：右上角简洁显示
- **物体计数**：左上角小字显示

### 使用方法

#### 基本使用
```bash
# 使用默认摄像头和模型
python scripts/realtime_onnx_inference.py

# 指定模型路径
python scripts/realtime_onnx_inference.py --model models/best.onnx

# 指定摄像头设备
python scripts/realtime_onnx_inference.py --camera 1
```

#### 高级参数
```bash
python scripts/realtime_onnx_inference.py \
    --model models/best.onnx \
    --camera 0 \
    --conf-threshold 0.3 \
    --nms-threshold 0.45 \
    --width 1920 \
    --height 1080 \
    --fps 30 \
    --save-video output_video.mp4
```

#### 参数说明
- `--model`: ONNX模型路径 (默认: models/best.onnx)
- `--camera`: 摄像头设备ID (默认: 0)
- `--conf-threshold`: 置信度阈值 (默认: 0.25)
- `--nms-threshold`: NMS阈值 (默认: 0.45)
- `--target-size`: 模型输入尺寸 (默认: 640)
- `--width`: 摄像头宽度 (默认: 1280)
- `--height`: 摄像头高度 (默认: 720)
- `--fps`: 摄像头帧率 (默认: 30)
- `--save-video`: 保存视频路径 (可选)
- `--no-display`: 不显示画面，仅推理

### 快捷键操作
- **q**: 退出程序
- **s**: 截图保存
- **r**: 重置性能统计

### 性能优化建议

#### CPU优化
- 降低摄像头分辨率: `--width 640 --height 480`
- 提高置信度阈值: `--conf-threshold 0.4`
- 使用较小的输入尺寸: `--target-size 416`

#### GPU加速
脚本会自动检测CUDA环境：
- 如果有CUDA：自动使用GPU加速
- 如果无CUDA：使用CPU推理

### 检测类别
支持12个类别的物体检测：
- apple (苹果)
- banana (香蕉)
- cake (蛋糕)
- chili (辣椒)
- cola (可乐)
- greenlight (绿灯)
- milk (牛奶)
- potato (土豆)
- redlight (红灯)
- tomato (番茄)
- watermelon (西瓜)

注：board类别会被自动过滤

### 输出信息
程序会每5秒输出一次统计信息：
```
📊 帧数: 150, 平均FPS: 30.2, 平均推理时间: 25.3ms, 当前检测: 2
🎯 检测到的物体:
   - apple: 0.856
   - cola: 0.743
```

### 故障排除

#### 摄像头问题
```bash
# 检查可用摄像头
ls /dev/video*  # Linux
# 或尝试不同的设备ID
python scripts/realtime_onnx_inference.py --camera 1
```

#### 性能问题
```bash
# 降低分辨率提升性能
python scripts/realtime_onnx_inference.py --width 640 --height 480

# 不显示画面，仅测试推理性能
python scripts/realtime_onnx_inference.py --no-display
```

#### 模型问题
```bash
# 检查模型文件是否存在
ls -la models/best.onnx

# 使用绝对路径
python scripts/realtime_onnx_inference.py --model /path/to/your/model.onnx
```

### 下一步：RKNN版本
测试ONNX版本效果满意后，可以使用RKNN版本部署到开发板：
```bash
python scripts/realtime_rknn_inference.py --model /path/to/model.rknn
```

### 依赖要求
```bash
pip install opencv-python numpy onnxruntime
# GPU版本 (可选)
pip install onnxruntime-gpu
```
