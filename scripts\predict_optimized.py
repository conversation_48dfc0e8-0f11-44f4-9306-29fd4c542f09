#!/usr/bin/env python3
"""
优化的YOLO预测脚本 - 专门用于场地测试
降低置信度阈值，减少漏检，优化中文显示
"""

import os
import sys
import cv2
import torch
import numpy as np
from pathlib import Path
from ultralytics import YOLO
import argparse
from datetime import datetime
import yaml
from PIL import Image, ImageDraw, ImageFont

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class OptimizedYOLOPredictor:
    def __init__(self, model_path, config_path="data/data.yaml", conf_threshold=0.25, iou_threshold=0.45):
        """
        初始化优化的YOLO预测器
        
        Args:
            model_path: 训练好的模型路径
            config_path: 数据配置文件路径
            conf_threshold: 置信度阈值 (降低到0.25减少漏检)
            iou_threshold: IoU阈值
        """
        self.project_root = project_root
        self.model_path = Path(model_path)
        self.config_path = self.project_root / config_path
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 验证文件存在
        if not self.model_path.exists():
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        # 加载模型
        self.model = YOLO(str(self.model_path))
        
        # 加载类别名称
        self.load_class_names()
        
        # 类别颜色映射
        self.colors = self.generate_colors()
        
        # 初始化中文字体
        self.font = self.load_chinese_font()
        
        print(f"🚀 优化预测器已初始化")
        print(f"使用设备: {self.device}")
        print(f"模型路径: {self.model_path}")
        print(f"置信度阈值: {self.conf_threshold} (降低以减少漏检)")
        print(f"IoU阈值: {self.iou_threshold}")
        print(f"中文字体: {'已加载' if self.font else '使用默认'}")
        
    def load_class_names(self):
        """加载类别名称"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        self.class_names = config['names']
        
        # 中文类别名称映射
        self.chinese_names = {
            'apple': '苹果',
            'banana': '香蕉', 
            'watermelon': '西瓜',
            'potato': '土豆',
            'tomato': '西红柿',
            'chili': '彩椒',
            'milk': '牛奶',
            'cola': '可乐',
            'cake': '蛋糕',
            'redlight': '红灯',
            'greenlight': '绿灯',
            'board': '板子'  # 额外类别，会被过滤
        }
        
        # 用户需要的11个类别
        self.target_classes = ['apple', 'banana', 'watermelon', 'potato', 'tomato', 
                              'chili', 'milk', 'cola', 'cake', 'redlight', 'greenlight']
        
    def generate_colors(self):
        """为每个类别生成颜色"""
        np.random.seed(42)  # 固定随机种子确保颜色一致
        colors = {}
        for i, class_name in enumerate(self.class_names):
            colors[class_name] = tuple(np.random.randint(0, 255, 3).tolist())
        return colors
        
    def load_chinese_font(self):
        """加载中文字体"""
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            "C:/Windows/Fonts/arial.ttf",   # Arial (备用)
        ]
        
        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    return ImageFont.truetype(font_path, 24)  # 增大字体
            except:
                continue
        
        # 如果都找不到，使用默认字体
        try:
            return ImageFont.load_default()
        except:
            return None
            
    def draw_chinese_text_with_background(self, image, text, position, text_color, bg_color, font_size=24):
        """在图像上绘制带背景的中文文字"""
        if self.font is None:
            # 如果没有字体，使用OpenCV绘制英文
            cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 0.7, text_color, 2)
            return image
            
        # 转换为PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 获取文字尺寸
        bbox = draw.textbbox((0, 0), text, font=self.font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 绘制背景矩形
        bg_x1, bg_y1 = position
        bg_x2 = bg_x1 + text_width + 10
        bg_y2 = bg_y1 + text_height + 10
        draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill=bg_color[::-1])  # BGR转RGB
        
        # 绘制文字
        draw.text((bg_x1 + 5, bg_y1 + 5), text, font=self.font, fill=text_color[::-1])  # BGR转RGB
        
        # 转换回OpenCV格式
        return cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
    def predict_field_images(self, input_dir, output_dir, show_stats=True):
        """
        专门用于场地图片的优化预测
        
        Args:
            input_dir: 输入图片目录
            output_dir: 输出结果目录
            show_stats: 是否显示统计信息
        """
        input_path = Path(input_dir)
        output_path = Path(output_dir)
        
        if not input_path.exists():
            raise FileNotFoundError(f"输入目录不存在: {input_path}")
            
        # 创建输出目录
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # 获取所有图片文件
        image_files = [f for f in input_path.iterdir() 
                      if f.suffix.lower() in image_extensions]
        
        if not image_files:
            print(f"❌ 在目录 {input_path} 中未找到图片文件")
            return
            
        print(f"🔍 开始优化预测，共 {len(image_files)} 张图片...")
        print(f"📁 结果将保存到: {output_path}")
        
        # 统计信息
        total_detections = 0
        detection_stats = {name: 0 for name in self.target_classes}
        confidence_stats = {name: [] for name in self.target_classes}
        
        for i, image_file in enumerate(image_files):
            print(f"处理 ({i+1}/{len(image_files)}): {image_file.name}")
            
            try:
                # 读取图片
                image = cv2.imread(str(image_file))
                if image is None:
                    print(f"⚠️ 无法读取图片: {image_file}")
                    continue
                
                # 进行预测
                results = self.model(
                    image,
                    conf=self.conf_threshold,
                    iou=self.iou_threshold,
                    device=self.device
                )
                
                # 绘制检测结果
                annotated_image = image.copy()
                
                if len(results) > 0 and results[0].boxes is not None:
                    boxes = results[0].boxes
                    
                    for j in range(len(boxes)):
                        # 获取边界框坐标
                        x1, y1, x2, y2 = boxes.xyxy[j].cpu().numpy().astype(int)
                        confidence = float(boxes.conf[j].cpu().numpy())
                        class_id = int(boxes.cls[j].cpu().numpy())
                        
                        # 获取类别名称
                        class_name = self.class_names[class_id]
                        
                        # 只处理用户需要的11个类别
                        if class_name not in self.target_classes:
                            continue
                            
                        # 获取中文名称
                        chinese_name = self.chinese_names.get(class_name, class_name)
                        
                        # 获取颜色
                        color = self.colors[class_name]
                        
                        # 绘制边界框
                        cv2.rectangle(annotated_image, (x1, y1), (x2, y2), color, 3)
                        
                        # 绘制标签
                        label = f"{chinese_name}: {confidence:.2f}"
                        
                        # 使用优化的中文绘制方法
                        annotated_image = self.draw_chinese_text_with_background(
                            annotated_image, label, (x1, y1 - 35), 
                            (255, 255, 255), color
                        )
                        
                        # 统计
                        detection_stats[class_name] += 1
                        confidence_stats[class_name].append(confidence)
                        total_detections += 1
                
                # 保存结果
                output_file = output_path / f"optimized_{image_file.name}"
                cv2.imwrite(str(output_file), annotated_image)
                
            except Exception as e:
                print(f"❌ 处理图片 {image_file.name} 时出错: {e}")
                
        # 显示统计信息
        if show_stats:
            self.print_detection_stats(detection_stats, confidence_stats, total_detections)
            
        print(f"\n✅ 优化预测完成!")
        print(f"📊 总检测数量: {total_detections}")
        print(f"📁 结果保存在: {output_path}")
        
    def print_detection_stats(self, detection_stats, confidence_stats, total_detections):
        """打印检测统计信息"""
        print(f"\n📊 检测统计报告:")
        print("=" * 60)
        print(f"总检测数量: {total_detections}")
        print(f"平均每张图片检测: {total_detections/len([f for f in Path('场地图片').iterdir() if f.suffix.lower() in {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}]):.1f}个物体")
        
        print(f"\n各类别详细统计:")
        print("-" * 60)
        
        for class_name in self.target_classes:
            count = detection_stats[class_name]
            if count > 0:
                chinese_name = self.chinese_names[class_name]
                avg_conf = np.mean(confidence_stats[class_name])
                min_conf = min(confidence_stats[class_name])
                max_conf = max(confidence_stats[class_name])
                
                print(f"{chinese_name:6} ({class_name:10}): {count:3}个 | "
                      f"平均置信度: {avg_conf:.3f} | "
                      f"范围: {min_conf:.3f}-{max_conf:.3f}")
        
        # 检测质量分析
        print(f"\n🎯 检测质量分析:")
        print("-" * 60)
        high_conf_count = sum(1 for class_name in self.target_classes 
                             for conf in confidence_stats[class_name] if conf >= 0.7)
        medium_conf_count = sum(1 for class_name in self.target_classes 
                               for conf in confidence_stats[class_name] if 0.4 <= conf < 0.7)
        low_conf_count = sum(1 for class_name in self.target_classes 
                            for conf in confidence_stats[class_name] if conf < 0.4)
        
        print(f"高置信度 (≥0.7): {high_conf_count}个 ({high_conf_count/total_detections*100:.1f}%)")
        print(f"中等置信度 (0.4-0.7): {medium_conf_count}个 ({medium_conf_count/total_detections*100:.1f}%)")
        print(f"低置信度 (<0.4): {low_conf_count}个 ({low_conf_count/total_detections*100:.1f}%)")

def main():
    parser = argparse.ArgumentParser(description='优化的YOLO场地预测脚本')
    parser.add_argument('--model', type=str, help='模型路径（不指定则自动查找最新模型）')
    parser.add_argument('--input', type=str, default='场地图片', help='输入图片目录')
    parser.add_argument('--output', type=str, default='optimized_results', help='输出目录')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--iou', type=float, default=0.45, help='IoU阈值')
    
    args = parser.parse_args()
    
    # 如果没有指定模型，自动查找最新的
    if args.model is None:
        runs_dir = Path("runs/train")
        if runs_dir.exists():
            train_dirs = [d for d in runs_dir.iterdir() if d.is_dir()]
            if train_dirs:
                latest_dir = max(train_dirs, key=lambda x: x.stat().st_mtime)
                best_model = latest_dir / "weights" / "best.pt"
                if best_model.exists():
                    args.model = str(best_model)
                    print(f"🎯 自动找到最新模型: {args.model}")
    
    if not args.model:
        print("❌ 未找到模型文件，请指定 --model 参数")
        return
    
    try:
        # 创建优化预测器
        predictor = OptimizedYOLOPredictor(
            model_path=args.model,
            conf_threshold=args.conf,
            iou_threshold=args.iou
        )
        
        # 进行优化预测
        predictor.predict_field_images(args.input, args.output)
        
        print(f"\n💡 提示:")
        print(f"- 如果还有漏检，可以进一步降低置信度: --conf 0.2")
        print(f"- 如果误检太多，可以提高置信度: --conf 0.4")
        print(f"- 检查 {args.output} 目录下的结果图片")
        
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
