#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版ONNX模型转RKNN并量化
使用RKNN-Toolkit 1.6.0进行模型量化和转换
"""

import os
import sys
import numpy as np
import argparse
from pathlib import Path

def install_rknn_toolkit():
    """安装RKNN-Toolkit 1.6.0"""
    print("检查RKNN-Toolkit安装...")
    
    try:
        from rknn.api import RKNN
        print("RKNN-Toolkit已安装")
        return True
    except ImportError:
        print("RKNN-Toolkit未安装")
        print("请手动安装RKNN-Toolkit 1.6.0")
        print("访问: https://github.com/rockchip-linux/rknn-toolkit")
        return False

def convert_onnx_to_rknn(onnx_path, rknn_path, quantization_dataset=None, 
                        target_platform='rk3588', img_size=640):
    """
    将ONNX模型转换为RKNN格式并量化
    """
    print(f"开始ONNX到RKNN转换和量化")
    print(f"输入ONNX: {onnx_path}")
    print(f"输出RKNN: {rknn_path}")
    print(f"目标平台: {target_platform}")
    print(f"图像尺寸: {img_size}")
    
    try:
        from rknn.api import RKNN
    except ImportError:
        print("RKNN-Toolkit未安装")
        return False
    
    # 创建RKNN对象
    rknn = RKNN(verbose=True)
    
    try:
        # 配置模型
        print("配置RKNN模型...")
        ret = rknn.config(
            mean_values=[[0, 0, 0]],           # 均值
            std_values=[[255, 255, 255]],      # 标准差 (归一化到0-1)
            target_platform=target_platform,   # 目标平台
            quantized_dtype='asymmetric_quantized-u8',  # 量化类型
            optimization_level=3,              # 优化级别
            output_optimize=1,                 # 输出优化
            compress_weight=True,              # 权重压缩
        )
        
        if ret != 0:
            print("RKNN配置失败")
            return False
        
        # 加载ONNX模型
        print("加载ONNX模型...")
        ret = rknn.load_onnx(model=onnx_path)
        if ret != 0:
            print("ONNX模型加载失败")
            return False
        
        # 构建模型
        print("构建RKNN模型...")
        
        # 准备量化数据集
        dataset = None
        if quantization_dataset and os.path.exists(quantization_dataset):
            print(f"加载量化数据集: {quantization_dataset}")
            dataset = np.load(quantization_dataset)
            print(f"量化数据集形状: {dataset.shape}")
            
            # RKNN需要的数据格式是列表
            dataset = [dataset[i:i+1] for i in range(min(100, len(dataset)))]
        else:
            print("未提供量化数据集，使用随机数据")
            # 生成随机数据用于量化
            random_data = np.random.rand(10, 3, img_size, img_size).astype(np.float32)
            dataset = [random_data[i:i+1] for i in range(10)]
        
        ret = rknn.build(do_quantization=True, dataset=dataset)
        if ret != 0:
            print("RKNN模型构建失败")
            return False
        
        # 导出RKNN模型
        print("导出RKNN模型...")
        ret = rknn.export_rknn(rknn_path)
        if ret != 0:
            print("RKNN模型导出失败")
            return False
        
        print("RKNN转换和量化成功!")
        
        # 获取模型信息
        model_size = os.path.getsize(rknn_path) / (1024 * 1024)  # MB
        print(f"RKNN模型大小: {model_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"RKNN转换过程中出错: {e}")
        return False
    
    finally:
        # 释放资源
        rknn.release()

def validate_rknn_model(rknn_path, test_data=None, img_size=640):
    """验证RKNN模型"""
    print(f"验证RKNN模型: {rknn_path}")
    
    try:
        from rknn.api import RKNN
    except ImportError:
        print("RKNN-Toolkit未安装，无法验证")
        return False
    
    rknn = RKNN(verbose=False)
    
    try:
        # 加载RKNN模型
        ret = rknn.load_rknn(rknn_path)
        if ret != 0:
            print("RKNN模型加载失败")
            return False
        
        # 初始化运行时环境
        ret = rknn.init_runtime()
        if ret != 0:
            print("RKNN运行时初始化失败")
            return False
        
        # 准备测试数据
        if test_data is None:
            test_data = np.random.rand(1, 3, img_size, img_size).astype(np.float32)
        
        # 推理测试
        outputs = rknn.inference(inputs=[test_data])
        
        if outputs is not None:
            print("RKNN模型验证通过")
            print(f"  输出数量: {len(outputs)}")
            for i, output in enumerate(outputs):
                print(f"  输出{i+1}形状: {output.shape}")
            return True
        else:
            print("RKNN推理失败")
            return False
            
    except Exception as e:
        print(f"RKNN验证过程中出错: {e}")
        return False
    
    finally:
        rknn.release()

def main():
    parser = argparse.ArgumentParser(description='Convert ONNX to RKNN with quantization')
    parser.add_argument('--onnx', type=str, required=True, help='Path to ONNX model')
    parser.add_argument('--output', type=str, help='Output RKNN path')
    parser.add_argument('--dataset', type=str, help='Quantization dataset (.npy)')
    parser.add_argument('--platform', type=str, default='rk3588', 
                       choices=['rk3588', 'rk3566', 'rk3568', 'rk3562'],
                       help='Target platform')
    parser.add_argument('--img-size', type=int, default=640, help='Input image size')
    parser.add_argument('--install', action='store_true', help='Install RKNN-Toolkit')
    
    args = parser.parse_args()
    
    # 检查RKNN-Toolkit
    if not install_rknn_toolkit():
        print("请先安装RKNN-Toolkit")
        return
    
    # 检查输入文件
    if not os.path.exists(args.onnx):
        print(f"ONNX文件不存在: {args.onnx}")
        return
    
    # 设置输出路径
    if args.output is None:
        args.output = args.onnx.replace('.onnx', '.rknn')
    
    # 创建输出目录
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # 转换模型
    success = convert_onnx_to_rknn(
        onnx_path=args.onnx,
        rknn_path=args.output,
        quantization_dataset=args.dataset,
        target_platform=args.platform,
        img_size=args.img_size
    )
    
    if success:
        # 验证模型
        validate_rknn_model(args.output, img_size=args.img_size)
        print(f"RKNN转换完成! 模型保存在: {args.output}")
    else:
        print("RKNN转换失败!")

if __name__ == '__main__':
    main()
