#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN模型实时摄像头推理脚本
基于修复后的RKNN推理流程，支持摄像头实时检测
"""

import cv2
import numpy as np
import time
import argparse
import os
from collections import deque

# 类别名称映射
CLASS_NAMES = {
    0: 'apple', 1: 'banana', 2: 'board', 3: 'cake', 4: 'chili', 5: 'cola',
    6: 'greenlight', 7: 'milk', 8: 'potato', 9: 'redlight', 10: 'tomato', 11: 'watermelon'
}

# 类别颜色映射 (BGR格式)
CLASS_COLORS = {
    0: (0, 255, 0),      # apple - 绿色
    1: (0, 255, 255),    # banana - 黄色
    2: (128, 128, 128),  # board - 灰色
    3: (255, 192, 203),  # cake - 粉色
    4: (0, 0, 255),      # chili - 红色
    5: (139, 69, 19),    # cola - 棕色
    6: (0, 255, 0),      # greenlight - 绿色
    7: (255, 255, 255),  # milk - 白色
    8: (0, 165, 255),    # potato - 橙色
    9: (0, 0, 255),      # redlight - 红色
    10: (0, 0, 255),     # tomato - 红色
    11: (0, 128, 0),     # watermelon - 深绿色
}

class RealTimeRKNNInference:
    def __init__(self, model_path, conf_threshold=0.25, nms_threshold=0.45, target_size=640):
        """
        初始化实时RKNN推理器
        
        Args:
            model_path: RKNN模型路径
            conf_threshold: 置信度阈值
            nms_threshold: NMS阈值
            target_size: 输入图像尺寸
        """
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.nms_threshold = nms_threshold
        self.target_size = target_size
        self.rknn = None
        
        # 性能统计
        self.fps_queue = deque(maxlen=30)  # 保存最近30帧的FPS
        self.inference_times = deque(maxlen=30)  # 保存最近30次推理时间
        
        # 初始化RKNN模型
        self._init_rknn()
    
    def _init_rknn(self):
        """初始化RKNN模型"""
        try:
            from rknnlite.api import RKNNLite
            
            print(f"🔧 加载RKNN模型: {self.model_path}")
            self.rknn = RKNNLite(verbose=False)
            
            ret = self.rknn.load_rknn(self.model_path)
            if ret != 0:
                raise RuntimeError(f"加载RKNN模型失败, ret: {ret}")
            
            ret = self.rknn.init_runtime(core_mask=RKNNLite.NPU_CORE_0_1_2)
            if ret != 0:
                raise RuntimeError(f"初始化RKNN运行时失败, ret: {ret}")
            
            print("✅ RKNN模型初始化成功")
            
        except ImportError:
            raise RuntimeError("❌ RKNNLite未安装，请安装rknnlite")
        except Exception as e:
            raise RuntimeError(f"❌ RKNN初始化失败: {e}")
    
    def preprocess_frame(self, frame):
        """
        预处理摄像头帧
        
        Args:
            frame: 摄像头帧 (BGR格式)
            
        Returns:
            input_data: 预处理后的输入数据
            scale: 缩放比例
            offset: 偏移量
            original_size: 原始尺寸
        """
        original_h, original_w = frame.shape[:2]
        
        # 计算缩放比例
        scale = min(self.target_size / original_h, self.target_size / original_w)
        new_h, new_w = int(original_h * scale), int(original_w * scale)
        
        # 缩放图像
        img_resized = cv2.resize(frame, (new_w, new_h))
        
        # 创建填充图像
        padded_img = np.full((self.target_size, self.target_size, 3), 114, dtype=np.uint8)
        top = (self.target_size - new_h) // 2
        left = (self.target_size - new_w) // 2
        padded_img[top:top+new_h, left:left+new_w] = img_resized
        
        # RKNN使用NHWC格式，需要RGB顺序（与ONNX一致），uint8类型
        # 注意：frame是BGR格式，需要转换为RGB
        padded_img_rgb = cv2.cvtColor(padded_img, cv2.COLOR_BGR2RGB)
        input_data = padded_img_rgb.astype(np.uint8)
        input_data = np.expand_dims(input_data, axis=0)
        
        return input_data, scale, (top, left), (original_h, original_w)
    
    def postprocess_detections(self, output, scale, offset, original_size):
        """
        后处理检测结果
        
        Args:
            output: 模型输出
            scale: 缩放比例
            offset: 偏移量
            original_size: 原始尺寸
            
        Returns:
            detections: 检测结果列表
        """
        if len(output.shape) == 3:
            output = output[0]
        
        if output.shape[0] == 16:
            output = output.T
        
        boxes = output[:, :4]
        scores = output[:, 4:]
        
        max_scores = np.max(scores, axis=1)
        class_ids = np.argmax(scores, axis=1)
        
        # 置信度过滤
        valid_indices = max_scores >= self.conf_threshold
        if not np.any(valid_indices):
            return []
        
        boxes = boxes[valid_indices]
        max_scores = max_scores[valid_indices]
        class_ids = class_ids[valid_indices]
        
        # 转换边界框格式 (center_x, center_y, width, height) -> (x1, y1, x2, y2)
        x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        boxes = np.column_stack([x1, y1, x2, y2])
        
        # NMS
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), max_scores.tolist(), 
                                  self.conf_threshold, self.nms_threshold)
        
        detections = []
        if len(indices) > 0:
            indices = indices.flatten()
            for i in indices:
                x1, y1, x2, y2 = boxes[i]
                conf = max_scores[i]
                class_id = class_ids[i]
                
                # 过滤board类别
                if class_id == 2:
                    continue
                
                if class_id not in CLASS_NAMES:
                    continue
                
                # 坐标转换回原始图像
                top, left = offset
                original_h, original_w = original_size
                
                x1 = (x1 - left) / scale
                y1 = (y1 - top) / scale
                x2 = (x2 - left) / scale
                y2 = (y2 - top) / scale
                
                # 边界检查
                x1 = max(0, min(x1, original_w))
                y1 = max(0, min(y1, original_h))
                x2 = max(0, min(x2, original_w))
                y2 = max(0, min(y2, original_h))
                
                detections.append({
                    'class_id': int(class_id),
                    'class_name': CLASS_NAMES[class_id],
                    'confidence': float(conf),
                    'bbox': [float(x1), float(y1), float(x2), float(y2)]
                })
        
        return detections
    
    def inference(self, frame):
        """
        对单帧进行推理
        
        Args:
            frame: 输入帧
            
        Returns:
            detections: 检测结果
            inference_time: 推理时间(ms)
        """
        # 预处理
        input_data, scale, offset, original_size = self.preprocess_frame(frame)
        
        # 推理
        start_time = time.time()
        outputs = self.rknn.inference(inputs=[input_data])
        inference_time = (time.time() - start_time) * 1000  # 转换为毫秒
        
        # 后处理
        detections = self.postprocess_detections(outputs[0], scale, offset, original_size)
        
        # 更新性能统计
        self.inference_times.append(inference_time)
        
        return detections, inference_time
    
    def draw_detections(self, frame, detections, inference_time):
        """
        在帧上绘制检测结果
        
        Args:
            frame: 输入帧
            detections: 检测结果
            inference_time: 推理时间
            
        Returns:
            annotated_frame: 标注后的帧
        """
        annotated_frame = frame.copy()
        
        # 绘制检测框
        for det in detections:
            x1, y1, x2, y2 = [int(coord) for coord in det['bbox']]
            class_id = det['class_id']
            class_name = det['class_name']
            confidence = det['confidence']
            
            # 获取颜色
            color = CLASS_COLORS.get(class_id, (255, 255, 255))
            
            # 绘制边界框
            cv2.rectangle(annotated_frame, (x1, y1), (x2, y2), color, 2)
            
            # 绘制标签
            label = f"{class_name}: {confidence:.2f}"
            label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]
            
            # 标签背景
            cv2.rectangle(annotated_frame, (x1, y1 - label_size[1] - 10), 
                         (x1 + label_size[0], y1), color, -1)
            
            # 标签文字
            cv2.putText(annotated_frame, label, (x1, y1 - 5), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)
        
        # 绘制性能信息
        self._draw_performance_info(annotated_frame, inference_time)
        
        return annotated_frame
    
    def _draw_performance_info(self, frame, inference_time):
        """绘制性能信息"""
        h, w = frame.shape[:2]
        
        # 计算平均推理时间
        avg_inference_time = np.mean(self.inference_times) if self.inference_times else 0
        
        # 计算FPS
        fps = 1000 / avg_inference_time if avg_inference_time > 0 else 0
        
        # 性能文本
        perf_text = [
            f"FPS: {fps:.1f}",
            f"Inference: {inference_time:.1f}ms",
            f"Avg Inference: {avg_inference_time:.1f}ms",
            f"Detections: {len(self.inference_times) if hasattr(self, 'last_detections') else 0}"
        ]
        
        # 绘制性能信息背景
        text_height = 25
        bg_height = len(perf_text) * text_height + 10
        cv2.rectangle(frame, (10, 10), (300, bg_height), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (300, bg_height), (255, 255, 255), 2)
        
        # 绘制性能文本
        for i, text in enumerate(perf_text):
            y = 30 + i * text_height
            cv2.putText(frame, text, (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
    
    def release(self):
        """释放资源"""
        if self.rknn:
            self.rknn.release()
            print("✅ RKNN资源已释放")


def main():
    parser = argparse.ArgumentParser(description='RKNN实时摄像头推理')
    parser.add_argument('--model', type=str, required=True, help='RKNN模型路径')
    parser.add_argument('--camera', type=int, default=0, help='摄像头设备ID')
    parser.add_argument('--conf-threshold', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--nms-threshold', type=float, default=0.45, help='NMS阈值')
    parser.add_argument('--target-size', type=int, default=640, help='输入图像尺寸')
    parser.add_argument('--width', type=int, default=1280, help='摄像头宽度')
    parser.add_argument('--height', type=int, default=720, help='摄像头高度')
    parser.add_argument('--fps', type=int, default=30, help='摄像头FPS')
    parser.add_argument('--save-video', type=str, help='保存视频路径')
    parser.add_argument('--display', action='store_true', default=True, help='显示实时画面')
    parser.add_argument('--no-display', action='store_true', help='不显示画面（仅推理）')

    args = parser.parse_args()

    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ RKNN模型文件不存在: {args.model}")
        return

    # 初始化推理器
    try:
        print("🚀 初始化RKNN推理器...")
        inference_engine = RealTimeRKNNInference(
            model_path=args.model,
            conf_threshold=args.conf_threshold,
            nms_threshold=args.nms_threshold,
            target_size=args.target_size
        )
    except Exception as e:
        print(f"❌ 推理器初始化失败: {e}")
        return

    # 初始化摄像头
    print(f"📷 初始化摄像头 (设备ID: {args.camera})...")
    cap = cv2.VideoCapture(args.camera)

    if not cap.isOpened():
        print(f"❌ 无法打开摄像头设备: {args.camera}")
        inference_engine.release()
        return

    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)
    cap.set(cv2.CAP_PROP_FPS, args.fps)

    # 获取实际摄像头参数
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    actual_fps = cap.get(cv2.CAP_PROP_FPS)

    print(f"📷 摄像头参数: {actual_width}x{actual_height} @ {actual_fps:.1f}FPS")

    # 初始化视频保存
    video_writer = None
    if args.save_video:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(args.save_video, fourcc, actual_fps,
                                     (actual_width, actual_height))
        print(f"📹 视频将保存到: {args.save_video}")

    # 显示设置
    display_enabled = args.display and not args.no_display
    if display_enabled:
        cv2.namedWindow('RKNN Real-time Inference', cv2.WINDOW_AUTOSIZE)
        print("🖥️  实时显示已启用")
        print("💡 按 'q' 退出，按 's' 截图")

    print("🎯 开始实时推理...")
    print("="*60)

    frame_count = 0
    start_time = time.time()

    try:
        while True:
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break

            frame_count += 1

            # 推理
            detections, inference_time = inference_engine.inference(frame)

            # 绘制检测结果
            annotated_frame = inference_engine.draw_detections(frame, detections, inference_time)

            # 保存视频
            if video_writer:
                video_writer.write(annotated_frame)

            # 显示画面
            if display_enabled:
                cv2.imshow('RKNN Real-time Inference', annotated_frame)

                # 处理按键
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 用户退出")
                    break
                elif key == ord('s'):
                    # 截图
                    screenshot_path = f"screenshot_{int(time.time())}.jpg"
                    cv2.imwrite(screenshot_path, annotated_frame)
                    print(f"📸 截图保存到: {screenshot_path}")

            # 打印检测结果（每30帧打印一次）
            if frame_count % 30 == 0:
                elapsed_time = time.time() - start_time
                avg_fps = frame_count / elapsed_time
                print(f"帧数: {frame_count}, 平均FPS: {avg_fps:.1f}, "
                      f"检测数量: {len(detections)}, 推理时间: {inference_time:.1f}ms")

                if detections:
                    for det in detections:
                        print(f"  - {det['class_name']}: {det['confidence']:.3f}")

    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在退出...")

    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理资源
        print("🧹 清理资源...")

        if cap:
            cap.release()
            print("✅ 摄像头已释放")

        if video_writer:
            video_writer.release()
            print("✅ 视频文件已保存")

        if display_enabled:
            cv2.destroyAllWindows()
            print("✅ 显示窗口已关闭")

        inference_engine.release()

        # 统计信息
        if frame_count > 0:
            total_time = time.time() - start_time
            avg_fps = frame_count / total_time
            print(f"📊 总计处理 {frame_count} 帧，平均FPS: {avg_fps:.1f}")


if __name__ == '__main__':
    main()
