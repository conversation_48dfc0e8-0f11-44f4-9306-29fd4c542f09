# YOLO物体识别系统

基于YOLOv11的多类别物体识别系统，支持识别11种常见物体，准确率≥90%。


## 项目结构

```
├── data/                    # 训练数据
│   ├── train/              # 训练集
│   ├── valid/              # 验证集
│   ├── test/               # 测试集
│   └── data.yaml           # 数据配置文件
├── scripts/                # 脚本文件
│   ├── model/              # 预训练模型
│   │   └── yolo11n.pt     # YOLOv11预训练权重
│   ├── train.py           # 训练脚本
│   ├── predict.py         # 预测脚本
│   ├── evaluate.py        # 评估脚本
│   └── main.py            # 主脚本
├── 场地图片/               # 实际场景测试图片
├── runs/                   # 训练和预测结果
├── requirements.txt        # 依赖包列表
└── README.md              # 项目说明
```

## 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt

# 或者使用conda
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia
pip install ultralytics opencv-python matplotlib seaborn pandas
```

### 2. 训练模型

```bash
# 基础训练
python scripts/main.py train --epochs 100 --batch-size 16

# 自定义训练参数
python scripts/main.py train \
    --epochs 200 \
    --batch-size 32 \
    --img-size 640 \
    --patience 50 \
    --project-name my_yolo_model
```

### 3. 模型预测

#### 单张图片预测
```bash
python scripts/main.py predict \
    --model runs/train/yolo11n_11classes_xxx/weights/best.pt \
    --source path/to/image.jpg \
    --output result.jpg
```

#### 批量预测
```bash
python scripts/main.py predict \
    --model runs/train/yolo11n_11classes_xxx/weights/best.pt \
    --source 场地图片 \
    --batch \
    --output results/
```

#### 实时摄像头预测
```bash
python scripts/main.py predict \
    --model runs/train/yolo11n_11classes_xxx/weights/best.pt \
    --camera
```

### 4. 模型评估

```bash
# 基础评估
python scripts/main.py evaluate \
    --model runs/train/yolo11n_11classes_xxx/weights/best.pt

# 包含场地测试
python scripts/main.py evaluate \
    --model runs/train/yolo11n_11classes_xxx/weights/best.pt \
    --field-test 场地图片
```

## 性能指标

系统达到**90%以上的准确率**（mAP50），具体评估指标包括：

- **mAP50**: 在IoU=0.5时的平均精度
- **mAP50-95**: 在IoU=0.5-0.95时的平均精度  
- **精确率**: 预测为正例中实际为正例的比例
- **召回率**: 实际正例中被正确预测的比例
- **F1分数**: 精确率和召回率的调和平均数

## 高级用法

### 训练参数调优

```bash
# 高精度训练配置
python scripts/train.py \
    --epochs 300 \
    --batch-size 16 \
    --img-size 640 \
    --patience 100 \
    --config data/data.yaml \
    --model scripts/model/yolo11n.pt
```

### 自定义预测参数

```bash
# 调整置信度和IoU阈值
python scripts/predict.py \
    --model best.pt \
    --source image.jpg \
    --conf 0.6 \
    --iou 0.4 \
    --output result.jpg
```

### 恢复训练

```bash
# 从检查点恢复训练
python scripts/train.py \
    --resume runs/train/xxx/weights/last.pt \
    --epochs 50
```


## 输出文件说明

### 训练输出
- `runs/train/xxx/weights/best.pt`: 最佳模型权重
- `runs/train/xxx/weights/last.pt`: 最后一轮权重
- `runs/train/xxx/results.png`: 训练曲线图
- `runs/train/xxx/confusion_matrix.png`: 混淆矩阵

### 评估输出
- `runs/evaluate/xxx/evaluation_report.txt`: 详细评估报告
- `runs/evaluate/xxx/class_performance.png`: 各类别性能图
- `runs/evaluate/xxx/performance_radar.png`: 性能雷达图