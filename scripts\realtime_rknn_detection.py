#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RKNN模型实时摄像头检测
"""

import cv2
import numpy as np
import time
import argparse
import os
from collections import deque

# 类别名称映射
CLASS_NAMES = {
    0: 'apple', 1: 'banana', 2: 'board', 3: 'cake', 4: 'chili', 5: 'cola',
    6: 'greenlight', 7: 'milk', 8: 'potato', 9: 'redlight', 10: 'tomato', 11: 'watermelon'
}

# 类别颜色映射 (BGR格式)
CLASS_COLORS = {
    0: (0, 255, 100),    # apple - 青绿色
    1: (0, 200, 255),    # banana - 橙黄色
    2: (128, 128, 128),  # board - 灰色
    3: (255, 100, 255),  # cake - 紫红色
    4: (0, 100, 255),    # chili - 橙红色
    5: (100, 50, 200),   # cola - 深紫色
    6: (0, 255, 0),      # greenlight - 纯绿色
    7: (255, 255, 255),  # milk - 白色
    8: (0, 150, 255),    # potato - 橙色
    9: (0, 50, 255),     # redlight - 红色
    10: (0, 100, 255),   # tomato - 橙红色
    11: (100, 255, 100), # watermelon - 浅绿色
}

class SettingsPanel:
    def __init__(self, initial_conf=0.25, initial_nms=0.45):
        self.show_settings = False
        self.conf_threshold = initial_conf
        self.nms_threshold = initial_nms
        self.conf_slider_pos = int(initial_conf * 100)
        self.nms_slider_pos = int(initial_nms * 100)
        
    def draw_settings_button(self, frame):
        """绘制设置按钮"""
        h, w = frame.shape[:2]
        
        # 设置按钮位置（左上角）
        btn_x, btn_y = 20, 60
        btn_w, btn_h = 80, 30
        
        # 绘制按钮背景
        color = (0, 255, 255) if not self.show_settings else (0, 200, 200)
        overlay = frame.copy()
        cv2.rectangle(overlay, (btn_x, btn_y), (btn_x + btn_w, btn_y + btn_h), (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)
        
        # 绘制按钮边框和文字
        cv2.rectangle(frame, (btn_x, btn_y), (btn_x + btn_w, btn_y + btn_h), color, 2)
        cv2.putText(frame, "Settings", (btn_x + 8, btn_y + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return (btn_x, btn_y, btn_w, btn_h)
    
    def draw_settings_panel(self, frame):
        """绘制设置面板"""
        if not self.show_settings:
            return
            
        h, w = frame.shape[:2]
        
        # 设置面板位置和尺寸
        panel_x, panel_y = 20, 100
        panel_w, panel_h = 300, 150
        
        # 绘制面板背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (panel_x, panel_y), 
                     (panel_x + panel_w, panel_y + panel_h), (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.6, overlay, 0.4, 0, frame)
        
        # 绘制面板边框
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_w, panel_y + panel_h), (0, 255, 255), 2)
        
        # 标题
        cv2.putText(frame, "Detection Settings", (panel_x + 10, panel_y + 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 置信度滑块
        conf_y = panel_y + 50
        cv2.putText(frame, f"Confidence: {self.conf_threshold:.2f}", 
                   (panel_x + 10, conf_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 置信度滑块轨道
        slider_x = panel_x + 10
        slider_y = conf_y + 15
        slider_w = 250
        cv2.line(frame, (slider_x, slider_y), (slider_x + slider_w, slider_y), (100, 100, 100), 3)
        
        # 置信度滑块手柄
        handle_x = slider_x + int(self.conf_slider_pos * slider_w / 100)
        cv2.circle(frame, (handle_x, slider_y), 8, (0, 255, 255), -1)
        cv2.circle(frame, (handle_x, slider_y), 8, (255, 255, 255), 2)
        
        # NMS滑块
        nms_y = panel_y + 100
        cv2.putText(frame, f"NMS: {self.nms_threshold:.2f}", 
                   (panel_x + 10, nms_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # NMS滑块轨道
        nms_slider_y = nms_y + 15
        cv2.line(frame, (slider_x, nms_slider_y), (slider_x + slider_w, nms_slider_y), (100, 100, 100), 3)
        
        # NMS滑块手柄
        nms_handle_x = slider_x + int(self.nms_slider_pos * slider_w / 100)
        cv2.circle(frame, (nms_handle_x, nms_slider_y), 8, (0, 255, 255), -1)
        cv2.circle(frame, (nms_handle_x, nms_slider_y), 8, (255, 255, 255), 2)
        
        # 返回滑块区域信息
        return {
            'conf_slider': (slider_x, slider_y - 10, slider_w, 20),
            'nms_slider': (slider_x, nms_slider_y - 10, slider_w, 20)
        }
    
    def handle_mouse_click(self, x, y, button_area, slider_areas=None):
        """处理鼠标点击"""
        btn_x, btn_y, btn_w, btn_h = button_area
        
        # 检查是否点击设置按钮
        if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
            self.show_settings = not self.show_settings
            return True
        
        # 如果设置面板打开，检查滑块点击
        if self.show_settings and slider_areas:
            # 置信度滑块
            if 'conf_slider' in slider_areas:
                sx, sy, sw, sh = slider_areas['conf_slider']
                if sx <= x <= sx + sw and sy <= y <= sy + sh:
                    self.conf_slider_pos = max(0, min(100, int((x - sx) * 100 / sw)))
                    self.conf_threshold = self.conf_slider_pos / 100.0
                    return True
            
            # NMS滑块
            if 'nms_slider' in slider_areas:
                sx, sy, sw, sh = slider_areas['nms_slider']
                if sx <= x <= sx + sw and sy <= y <= sy + sh:
                    self.nms_slider_pos = max(0, min(100, int((x - sx) * 100 / sw)))
                    self.nms_threshold = self.nms_slider_pos / 100.0
                    return True
        
        return False

class RealTimeRKNNInference:
    def __init__(self, model_path, conf_threshold=0.25, nms_threshold=0.45, target_size=640):
        """
        初始化实时RKNN推理器
        
        Args:
            model_path: RKNN模型路径
            conf_threshold: 置信度阈值
            nms_threshold: NMS阈值
            target_size: 输入图像尺寸
        """
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.nms_threshold = nms_threshold
        self.target_size = target_size
        self.rknn = None
        
        # 性能统计
        self.fps_queue = deque(maxlen=30)
        self.inference_times = deque(maxlen=30)
        
        # 设置面板
        self.settings = SettingsPanel(conf_threshold, nms_threshold)
        
        # 初始化RKNN模型
        self._init_rknn()
    
    def _init_rknn(self):
        """初始化RKNN模型"""
        try:
            from rknnlite.api import RKNNLite
            
            print(f"🔧 加载RKNN模型: {self.model_path}")
            self.rknn = RKNNLite(verbose=False)
            
            ret = self.rknn.load_rknn(self.model_path)
            if ret != 0:
                raise RuntimeError(f"加载RKNN模型失败, ret: {ret}")
            
            ret = self.rknn.init_runtime(core_mask=RKNNLite.NPU_CORE_0_1_2)
            if ret != 0:
                raise RuntimeError(f"初始化RKNN运行时失败, ret: {ret}")
            
            print("✅ RKNN模型初始化成功")
            
        except ImportError:
            raise RuntimeError("❌ RKNNLite未安装，请安装rknnlite")
        except Exception as e:
            raise RuntimeError(f"❌ RKNN初始化失败: {e}")
    
    def preprocess_frame(self, frame):
        """
        预处理摄像头帧
        
        Args:
            frame: 摄像头帧 (BGR格式)
            
        Returns:
            input_data: 预处理后的输入数据
            scale: 缩放比例
            offset: 偏移量
            original_size: 原始尺寸
        """
        original_h, original_w = frame.shape[:2]
        
        # 计算缩放比例
        scale = min(self.target_size / original_h, self.target_size / original_w)
        new_h, new_w = int(original_h * scale), int(original_w * scale)
        
        # 缩放图像
        img_resized = cv2.resize(frame, (new_w, new_h))
        
        # 创建填充图像
        padded_img = np.full((self.target_size, self.target_size, 3), 114, dtype=np.uint8)
        top = (self.target_size - new_h) // 2
        left = (self.target_size - new_w) // 2
        padded_img[top:top+new_h, left:left+new_w] = img_resized
        
        # RKNN使用NHWC格式，需要RGB顺序，uint8类型
        padded_img_rgb = cv2.cvtColor(padded_img, cv2.COLOR_BGR2RGB)
        input_data = padded_img_rgb.astype(np.uint8)
        input_data = np.expand_dims(input_data, axis=0)
        
        return input_data, scale, (top, left), (original_h, original_w)

    def postprocess_detections(self, output, scale, offset, original_size):
        """
        后处理检测结果

        Args:
            output: 模型输出
            scale: 缩放比例
            offset: 偏移量
            original_size: 原始尺寸

        Returns:
            detections: 检测结果列表
        """
        # 使用动态阈值
        conf_threshold = self.settings.conf_threshold
        nms_threshold = self.settings.nms_threshold

        if len(output.shape) == 3:
            output = output[0]

        if output.shape[0] == 16:
            output = output.T

        boxes = output[:, :4]
        scores = output[:, 4:]

        max_scores = np.max(scores, axis=1)
        class_ids = np.argmax(scores, axis=1)

        # 置信度过滤
        valid_indices = max_scores >= conf_threshold
        if not np.any(valid_indices):
            return []

        boxes = boxes[valid_indices]
        max_scores = max_scores[valid_indices]
        class_ids = class_ids[valid_indices]

        # 转换边界框格式 (center_x, center_y, width, height) -> (x1, y1, x2, y2)
        x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        boxes = np.column_stack([x1, y1, x2, y2])

        # NMS
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), max_scores.tolist(),
                                  conf_threshold, nms_threshold)

        detections = []
        if len(indices) > 0:
            indices = indices.flatten()
            for i in indices:
                x1, y1, x2, y2 = boxes[i]
                conf = max_scores[i]
                class_id = class_ids[i]

                # 过滤board类别
                if class_id == 2:
                    continue

                if class_id not in CLASS_NAMES:
                    continue

                # 坐标转换回原始图像
                top, left = offset
                original_h, original_w = original_size

                x1 = (x1 - left) / scale
                y1 = (y1 - top) / scale
                x2 = (x2 - left) / scale
                y2 = (y2 - top) / scale

                # 边界检查
                x1 = max(0, min(x1, original_w))
                y1 = max(0, min(y1, original_h))
                x2 = max(0, min(x2, original_w))
                y2 = max(0, min(y2, original_h))

                detections.append({
                    'class_id': int(class_id),
                    'class_name': CLASS_NAMES[class_id],
                    'confidence': float(conf),
                    'bbox': [float(x1), float(y1), float(x2), float(y2)]
                })

        return detections

    def inference(self, frame):
        """
        对单帧进行推理

        Args:
            frame: 输入帧

        Returns:
            detections: 检测结果
            inference_time: 推理时间(ms)
        """
        # 预处理
        input_data, scale, offset, original_size = self.preprocess_frame(frame)

        # 推理
        start_time = time.time()
        outputs = self.rknn.inference(inputs=[input_data])
        inference_time = (time.time() - start_time) * 1000  # 转换为毫秒

        # 后处理
        detections = self.postprocess_detections(outputs[0], scale, offset, original_size)

        # 更新性能统计
        self.inference_times.append(inference_time)

        return detections, inference_time

    def draw_corner_box(self, frame, x1, y1, x2, y2, color, thickness=2, corner_length=20):
        """
        绘制四角边线框

        Args:
            frame: 图像帧
            x1, y1, x2, y2: 边界框坐标
            color: 颜色
            thickness: 线条粗细
            corner_length: 角线长度
        """
        # 左上角
        cv2.line(frame, (x1, y1), (x1 + corner_length, y1), color, thickness)
        cv2.line(frame, (x1, y1), (x1, y1 + corner_length), color, thickness)

        # 右上角
        cv2.line(frame, (x2, y1), (x2 - corner_length, y1), color, thickness)
        cv2.line(frame, (x2, y1), (x2, y1 + corner_length), color, thickness)

        # 左下角
        cv2.line(frame, (x1, y2), (x1 + corner_length, y2), color, thickness)
        cv2.line(frame, (x1, y2), (x1, y2 - corner_length), color, thickness)

        # 右下角
        cv2.line(frame, (x2, y2), (x2 - corner_length, y2), color, thickness)
        cv2.line(frame, (x2, y2), (x2, y2 - corner_length), color, thickness)

    def draw_detections(self, frame, detections, inference_time):
        """
        在帧上绘制检测结果

        Args:
            frame: 输入帧
            detections: 检测结果
            inference_time: 推理时间

        Returns:
            annotated_frame: 标注后的帧
            button_area: 设置按钮区域
            slider_areas: 滑块区域
        """
        annotated_frame = frame.copy()

        # 绘制检测框
        for det in detections:
            x1, y1, x2, y2 = [int(coord) for coord in det['bbox']]
            class_id = det['class_id']
            class_name = det['class_name']
            confidence = det['confidence']

            # 获取颜色
            color = CLASS_COLORS.get(class_id, (0, 255, 255))

            # 绘制淡色蒙版
            mask_color = tuple(int(c * 0.3) for c in color)
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (x1, y1), (x2, y2), mask_color, -1)
            cv2.addWeighted(annotated_frame, 0.8, overlay, 0.2, 0, annotated_frame)

            # 绘制四角边线框
            corner_length = min(30, (x2-x1)//4, (y2-y1)//4)
            self.draw_corner_box(annotated_frame, x1, y1, x2, y2, color, 3, corner_length)

            # 绘制标签
            label = f"{class_name} {confidence:.2f}"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            font_thickness = 2

            # 计算文字尺寸
            (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, font_thickness)

            # 标签位置
            label_x = x1
            label_y = y1 - 10

            if label_y - text_height < 0:
                label_y = y1 + text_height + 10

            # 绘制标签背景
            bg_x1 = label_x - 5
            bg_y1 = label_y - text_height - 5
            bg_x2 = label_x + text_width + 5
            bg_y2 = label_y + 5

            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (bg_x1, bg_y1), (bg_x2, bg_y2), (0, 0, 0), -1)
            cv2.addWeighted(annotated_frame, 0.7, overlay, 0.3, 0, annotated_frame)

            cv2.rectangle(annotated_frame, (bg_x1, bg_y1), (bg_x2, bg_y2), color, 1)
            cv2.putText(annotated_frame, label, (label_x, label_y), font, font_scale, color, font_thickness)

        # 绘制性能信息
        self._draw_performance_info(annotated_frame, inference_time, len(detections))

        # 绘制设置按钮
        button_area = self.settings.draw_settings_button(annotated_frame)

        # 绘制设置面板
        slider_areas = self.settings.draw_settings_panel(annotated_frame)

        return annotated_frame, button_area, slider_areas

    def _draw_performance_info(self, frame, inference_time, detection_count):
        """绘制性能信息"""
        h, w = frame.shape[:2]

        # 计算平均推理时间和FPS
        avg_inference_time = np.mean(self.inference_times) if self.inference_times else 0
        fps = 1000 / avg_inference_time if avg_inference_time > 0 else 0

        # FPS文本
        fps_text = f"FPS: {fps:.1f}"

        # 绘制FPS信息（右上角）
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        font_thickness = 2

        (text_width, text_height), baseline = cv2.getTextSize(fps_text, font, font_scale, font_thickness)

        fps_x = w - text_width - 20
        fps_y = 30

        # FPS背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (fps_x - 10, fps_y - text_height - 5),
                     (fps_x + text_width + 10, fps_y + 5), (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)

        cv2.rectangle(frame, (fps_x - 10, fps_y - text_height - 5),
                     (fps_x + text_width + 10, fps_y + 5), (0, 255, 255), 1)
        cv2.putText(frame, fps_text, (fps_x, fps_y), font, font_scale, (0, 255, 255), font_thickness)

        # 检测数量
        if detection_count > 0:
            det_text = f"Objects: {detection_count}"
            det_font_scale = 0.5
            (det_width, det_height), _ = cv2.getTextSize(det_text, font, det_font_scale, 1)

            det_x = 20
            det_y = 30

            overlay = frame.copy()
            cv2.rectangle(overlay, (det_x - 5, det_y - det_height - 5),
                         (det_x + det_width + 5, det_y + 5), (0, 0, 0), -1)
            cv2.addWeighted(frame, 0.8, overlay, 0.2, 0, frame)

            cv2.putText(frame, det_text, (det_x, det_y), font, det_font_scale, (100, 255, 100), 1)

    def handle_mouse_event(self, event, x, y, flags, param):
        """处理鼠标事件"""
        if event == cv2.EVENT_LBUTTONDOWN:
            button_area, slider_areas = param
            self.settings.handle_mouse_click(x, y, button_area, slider_areas)

    def release(self):
        """释放资源"""
        if self.rknn:
            self.rknn.release()
            print("✅ RKNN资源已释放")


def mouse_callback(event, x, y, flags, param):
    """鼠标回调函数"""
    inference_engine, button_area, slider_areas = param
    if event == cv2.EVENT_LBUTTONDOWN:
        inference_engine.settings.handle_mouse_click(x, y, button_area, slider_areas)


def main():
    parser = argparse.ArgumentParser(description='RKNN实时摄像头检测')
    parser.add_argument('--model', type=str, default='/home/<USER>/tool/yolov11_rk3588_fp16.rknn', help='RKNN模型路径')
    parser.add_argument('--camera', type=int, default=0, help='摄像头设备ID')
    parser.add_argument('--conf-threshold', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--nms-threshold', type=float, default=0.45, help='NMS阈值')
    parser.add_argument('--target-size', type=int, default=640, help='输入图像尺寸')
    parser.add_argument('--width', type=int, default=1280, help='摄像头宽度')
    parser.add_argument('--height', type=int, default=720, help='摄像头高度')
    parser.add_argument('--fps', type=int, default=30, help='摄像头FPS')
    parser.add_argument('--save-video', type=str, help='保存视频路径')

    args = parser.parse_args()

    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ RKNN模型文件不存在: {args.model}")
        return

    # 初始化推理器
    try:
        print("🚀 初始化RKNN推理器...")
        inference_engine = RealTimeRKNNInference(
            model_path=args.model,
            conf_threshold=args.conf_threshold,
            nms_threshold=args.nms_threshold,
            target_size=args.target_size
        )
    except Exception as e:
        print(f"❌ 推理器初始化失败: {e}")
        return

    # 初始化摄像头
    print(f"📷 初始化摄像头 (设备ID: {args.camera})...")
    cap = cv2.VideoCapture(args.camera)

    if not cap.isOpened():
        print(f"❌ 无法打开摄像头设备: {args.camera}")
        inference_engine.release()
        return

    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)
    cap.set(cv2.CAP_PROP_FPS, args.fps)

    # 获取实际摄像头参数
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    actual_fps = cap.get(cv2.CAP_PROP_FPS)

    print(f"📷 摄像头参数: {actual_width}x{actual_height} @ {actual_fps:.1f}FPS")

    # 初始化视频保存
    video_writer = None
    if args.save_video:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(args.save_video, fourcc, actual_fps,
                                     (actual_width, actual_height))
        print(f"📹 视频将保存到: {args.save_video}")

    # 显示设置
    window_name = 'RKNN Real-time Detection'
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    print("🖥️  实时检测已启用")
    print("💡 按 'q' 退出，按 's' 截图，点击Settings调整参数")

    print("🎯 开始实时推理...")
    print("="*60)

    frame_count = 0
    start_time = time.time()
    last_print_time = time.time()
    button_area = None
    slider_areas = None

    try:
        while True:
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break

            frame_count += 1

            # 推理
            detections, inference_time = inference_engine.inference(frame)

            # 绘制检测结果
            annotated_frame, button_area, slider_areas = inference_engine.draw_detections(
                frame, detections, inference_time)

            # 设置鼠标回调
            cv2.setMouseCallback(window_name, mouse_callback,
                               (inference_engine, button_area, slider_areas))

            # 保存视频
            if video_writer:
                video_writer.write(annotated_frame)

            # 显示画面
            cv2.imshow(window_name, annotated_frame)

            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("🛑 用户退出")
                break
            elif key == ord('s'):
                # 截图
                screenshot_path = f"screenshot_{int(time.time())}.jpg"
                cv2.imwrite(screenshot_path, annotated_frame)
                print(f"📸 截图保存到: {screenshot_path}")

            # 打印检测结果（每5秒打印一次）
            current_time = time.time()
            if current_time - last_print_time >= 5.0:
                elapsed_time = current_time - start_time
                avg_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                avg_inference = np.mean(inference_engine.inference_times) if inference_engine.inference_times else 0

                print(f"📊 帧数: {frame_count}, 平均FPS: {avg_fps:.1f}, "
                      f"平均推理时间: {avg_inference:.1f}ms, 当前检测: {len(detections)}")
                print(f"🎛️  当前设置: 置信度={inference_engine.settings.conf_threshold:.2f}, "
                      f"NMS={inference_engine.settings.nms_threshold:.2f}")

                if detections:
                    print("🎯 检测到的物体:")
                    for det in detections:
                        print(f"   - {det['class_name']}: {det['confidence']:.3f}")

                last_print_time = current_time

    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在退出...")

    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理资源
        print("🧹 清理资源...")

        if cap:
            cap.release()
            print("✅ 摄像头已释放")

        if video_writer:
            video_writer.release()
            print("✅ 视频文件已保存")

        cv2.destroyAllWindows()
        print("✅ 显示窗口已关闭")

        inference_engine.release()

        # 最终统计信息
        if frame_count > 0:
            total_time = time.time() - start_time
            avg_fps = frame_count / total_time
            avg_inference = np.mean(inference_engine.inference_times) if inference_engine.inference_times else 0
            print(f"📊 总计处理 {frame_count} 帧")
            print(f"📊 平均FPS: {avg_fps:.1f}")
            print(f"📊 平均推理时间: {avg_inference:.1f}ms")


if __name__ == '__main__':
    main()
