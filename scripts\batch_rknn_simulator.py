#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量RKNN仿真测试脚本
测试多张图片，生成详细的测试报告
"""

import os
import glob
import time
import json
from pathlib import Path
import argparse
from datetime import datetime

def run_single_test(model_path, image_path, output_dir, conf_threshold=0.25):
    """运行单个图片的测试"""
    import subprocess
    import sys
    
    cmd = [
        sys.executable, 
        "scripts/rknn_simulator_windows.py",
        "--model", model_path,
        "--image", image_path,
        "--output", output_dir,
        "--conf", str(conf_threshold)
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        return {
            'success': result.returncode == 0,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'returncode': result.returncode
        }
    except Exception as e:
        return {
            'success': False,
            'stdout': '',
            'stderr': str(e),
            'returncode': -1
        }

def parse_detection_results(stdout):
    """从输出中解析检测结果"""
    detections = []
    lines = stdout.split('\n')
    
    in_results = False
    for line in lines:
        if "RKNN仿真检测结果" in line:
            in_results = True
            continue
        elif in_results and line.strip().startswith(('1.', '2.', '3.', '4.', '5.')):
            # 解析检测结果行，例如: "  1. 土豆: 0.856 (位置: [123, 456, 789, 012])"
            try:
                parts = line.strip().split(': ')
                if len(parts) >= 2:
                    class_name = parts[1].split(':')[0].strip()
                    conf_part = parts[1].split(':')[1].strip()
                    confidence = float(conf_part.split(' ')[0])
                    detections.append({
                        'class_name': class_name,
                        'confidence': confidence
                    })
            except:
                continue
        elif in_results and "结果保存至:" in line:
            break
    
    return detections

def main():
    parser = argparse.ArgumentParser(description='批量RKNN仿真测试')
    parser.add_argument('--model', type=str, required=True, help='RKNN模型路径')
    parser.add_argument('--images', type=str, default='场地图片/*.jpg', help='图片路径模式')
    parser.add_argument('--output', type=str, default='batch_rknn_results', help='输出目录')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--max-images', type=int, default=10, help='最大测试图片数量')
    
    args = parser.parse_args()
    
    print("🚀 批量RKNN仿真测试")
    print("="*80)
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ RKNN模型文件不存在: {args.model}")
        return
    
    # 获取图片列表
    image_files = glob.glob(args.images)
    if not image_files:
        print(f"❌ 未找到图片文件: {args.images}")
        return
    
    # 限制图片数量
    if len(image_files) > args.max_images:
        image_files = image_files[:args.max_images]
        print(f"⚠️  限制测试图片数量为 {args.max_images} 张")
    
    print(f"📁 找到 {len(image_files)} 张图片")
    print(f"🎯 模型: {args.model}")
    print(f"📊 置信度阈值: {args.conf}")
    print("="*80)
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join(args.output, f"test_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    
    # 测试结果
    test_results = []
    successful_tests = 0
    total_detections = 0
    class_counts = {}
    
    # 开始测试
    start_time = time.time()
    
    for i, image_path in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] 测试: {Path(image_path).name}")
        print("-" * 60)
        
        test_start = time.time()
        result = run_single_test(args.model, image_path, output_dir, args.conf)
        test_time = time.time() - test_start
        
        if result['success']:
            print("✅ 测试成功")
            successful_tests += 1
            
            # 解析检测结果
            detections = parse_detection_results(result['stdout'])
            total_detections += len(detections)
            
            print(f"🎯 检测到 {len(detections)} 个物体:")
            for det in detections:
                class_name = det['class_name']
                confidence = det['confidence']
                print(f"   - {class_name}: {confidence:.3f}")
                
                # 统计类别
                if class_name in class_counts:
                    class_counts[class_name] += 1
                else:
                    class_counts[class_name] = 1
        else:
            print("❌ 测试失败")
            print(f"错误: {result['stderr']}")
        
        print(f"⏱️  耗时: {test_time:.2f}s")
        
        # 保存单个测试结果
        test_results.append({
            'image': image_path,
            'image_name': Path(image_path).name,
            'success': result['success'],
            'test_time': test_time,
            'detections': parse_detection_results(result['stdout']) if result['success'] else [],
            'error': result['stderr'] if not result['success'] else None
        })
    
    total_time = time.time() - start_time
    
    # 生成测试报告
    print("\n" + "="*80)
    print("📊 测试报告")
    print("="*80)
    print(f"总测试时间: {total_time:.2f}s")
    print(f"平均每张图片: {total_time/len(image_files):.2f}s")
    print(f"成功测试: {successful_tests}/{len(image_files)} ({successful_tests/len(image_files)*100:.1f}%)")
    print(f"总检测数: {total_detections}")
    print(f"平均每张图片检测数: {total_detections/len(image_files):.1f}")
    
    print(f"\n📈 类别统计:")
    if class_counts:
        for class_name, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_detections * 100 if total_detections > 0 else 0
            print(f"   {class_name}: {count} 次 ({percentage:.1f}%)")
    else:
        print("   无检测结果")
    
    # 保存详细报告
    report = {
        'test_info': {
            'model': args.model,
            'images_pattern': args.images,
            'confidence_threshold': args.conf,
            'timestamp': timestamp,
            'total_time': total_time,
            'total_images': len(image_files),
            'successful_tests': successful_tests,
            'total_detections': total_detections
        },
        'class_statistics': class_counts,
        'test_results': test_results
    }
    
    report_path = os.path.join(output_dir, 'test_report.json')
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成简化的文本报告
    txt_report_path = os.path.join(output_dir, 'test_summary.txt')
    with open(txt_report_path, 'w', encoding='utf-8') as f:
        f.write("RKNN仿真测试报告\n")
        f.write("="*50 + "\n")
        f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"模型文件: {args.model}\n")
        f.write(f"测试图片: {len(image_files)} 张\n")
        f.write(f"成功率: {successful_tests/len(image_files)*100:.1f}%\n")
        f.write(f"总检测数: {total_detections}\n\n")
        
        f.write("类别统计:\n")
        f.write("-" * 30 + "\n")
        for class_name, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_detections * 100 if total_detections > 0 else 0
            f.write(f"{class_name}: {count} 次 ({percentage:.1f}%)\n")
        
        f.write("\n详细结果:\n")
        f.write("-" * 30 + "\n")
        for result in test_results:
            f.write(f"{result['image_name']}: ")
            if result['success']:
                det_names = [d['class_name'] for d in result['detections']]
                f.write(f"{len(result['detections'])} 个检测 - {', '.join(det_names)}\n")
            else:
                f.write("失败\n")
    
    print(f"\n📁 详细报告保存至:")
    print(f"   JSON: {report_path}")
    print(f"   文本: {txt_report_path}")
    print(f"   图片: {output_dir}/")
    
    print(f"\n💡 测试完成！您可以将这些结果发给客户进行对比")
    print(f"💡 如果客户的结果与此不同，说明是客户环境问题")

if __name__ == '__main__':
    main()
