#!/usr/bin/env python3
"""
YOLO模型评估脚本
评估模型在测试集上的性能，生成详细的评估报告
"""

import os
import sys
import yaml
import torch
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from ultralytics import YOLO
import argparse
from datetime import datetime
import numpy as np

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class YOLOEvaluator:
    def __init__(self, model_path, config_path="data/data.yaml"):
        """
        初始化YOLO评估器
        
        Args:
            model_path: 训练好的模型路径
            config_path: 数据配置文件路径
        """
        self.project_root = project_root
        self.model_path = Path(model_path)
        self.config_path = self.project_root / config_path
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 验证文件存在
        if not self.model_path.exists():
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        # 加载模型
        self.model = YOLO(str(self.model_path))
        
        # 加载类别名称
        self.load_class_names()
        
        print(f"使用设备: {self.device}")
        print(f"模型路径: {self.model_path}")
        print(f"支持的类别: {self.class_names}")
        
    def load_class_names(self):
        """加载类别名称"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        self.class_names = config['names']
        
        # 中文类别名称映射
        self.chinese_names = {
            'apple': '苹果',
            'banana': '香蕉', 
            'watermelon': '西瓜',
            'potato': '土豆',
            'tomato': '西红柿',
            'chili': '彩椒',
            'milk': '牛奶',
            'cola': '可乐',
            'cake': '蛋糕',
            'redlight': '红灯',
            'greenlight': '绿灯',
            'board': '板子'  # 额外类别
        }
        
    def evaluate(self, save_dir=None):
        """
        评估模型性能
        
        Args:
            save_dir: 保存评估结果的目录
            
        Returns:
            results: 评估结果
        """
        if save_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_dir = self.project_root / 'runs' / 'evaluate' / f"eval_{timestamp}"
        else:
            save_dir = Path(save_dir)
            
        save_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"开始评估模型...")
        print(f"结果将保存到: {save_dir}")
        
        # 在验证集上评估
        results = self.model.val(
            data=str(self.config_path),
            device=self.device,
            save_json=True,
            save_hybrid=True,
            plots=True,
            project=str(save_dir.parent),
            name=save_dir.name
        )
        
        # 生成详细报告
        self.generate_report(results, save_dir)
        
        return results
        
    def generate_report(self, results, save_dir):
        """
        生成详细的评估报告
        
        Args:
            results: 评估结果
            save_dir: 保存目录
        """
        report_file = save_dir / "evaluation_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("YOLO模型评估报告\n")
            f.write("=" * 50 + "\n\n")
            
            # 基本信息
            f.write(f"模型路径: {self.model_path}\n")
            f.write(f"评估时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"设备: {self.device}\n\n")
            
            # 整体性能指标
            f.write("整体性能指标:\n")
            f.write("-" * 30 + "\n")
            f.write(f"mAP50: {results.box.map50:.4f}\n")
            f.write(f"mAP50-95: {results.box.map:.4f}\n")
            f.write(f"精确率 (Precision): {results.box.mp:.4f}\n")
            f.write(f"召回率 (Recall): {results.box.mr:.4f}\n")
            f.write(f"F1分数: {2 * results.box.mp * results.box.mr / (results.box.mp + results.box.mr):.4f}\n\n")
            
            # 目标准确率检查
            f.write("目标准确率检查:\n")
            f.write("-" * 30 + "\n")
            target_accuracy = 0.90
            if results.box.map50 >= target_accuracy:
                f.write(f"✅ 达到目标准确率! mAP50: {results.box.map50:.4f} >= {target_accuracy}\n")
            else:
                f.write(f"❌ 未达到目标准确率. mAP50: {results.box.map50:.4f} < {target_accuracy}\n")
                f.write("建议改进措施:\n")
                f.write("1. 增加训练轮数\n")
                f.write("2. 调整学习率\n")
                f.write("3. 增加数据增强\n")
                f.write("4. 收集更多训练数据\n")
                f.write("5. 调整模型架构\n")
            f.write("\n")
            
            # 各类别性能
            f.write("各类别性能:\n")
            f.write("-" * 30 + "\n")
            if hasattr(results.box, 'maps') and results.box.maps is not None:
                for i, (class_name, map_score) in enumerate(zip(self.class_names, results.box.maps)):
                    chinese_name = self.chinese_names.get(class_name, class_name)
                    f.write(f"{chinese_name}({class_name}): mAP50 = {map_score:.4f}\n")
            f.write("\n")
            
            # 推理速度
            if hasattr(results, 'speed'):
                f.write("推理速度:\n")
                f.write("-" * 30 + "\n")
                f.write(f"预处理: {results.speed['preprocess']:.2f}ms\n")
                f.write(f"推理: {results.speed['inference']:.2f}ms\n")
                f.write(f"后处理: {results.speed['postprocess']:.2f}ms\n")
                total_time = sum(results.speed.values())
                f.write(f"总时间: {total_time:.2f}ms\n")
                f.write(f"FPS: {1000/total_time:.1f}\n\n")
                
        print(f"评估报告已保存到: {report_file}")
        
        # 生成性能图表
        self.plot_performance(results, save_dir)
        
    def plot_performance(self, results, save_dir):
        """
        绘制性能图表
        
        Args:
            results: 评估结果
            save_dir: 保存目录
        """
        plt.style.use('default')
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']  # 支持中文显示
        plt.rcParams['axes.unicode_minus'] = False
        
        # 1. 各类别mAP50柱状图
        if hasattr(results.box, 'maps') and results.box.maps is not None:
            fig, ax = plt.subplots(figsize=(12, 8))
            
            class_names_cn = [self.chinese_names.get(name, name) for name in self.class_names]
            maps = results.box.maps
            
            bars = ax.bar(range(len(class_names_cn)), maps, color='skyblue', alpha=0.7)
            ax.set_xlabel('类别')
            ax.set_ylabel('mAP50')
            ax.set_title('各类别mAP50性能')
            ax.set_xticks(range(len(class_names_cn)))
            ax.set_xticklabels(class_names_cn, rotation=45, ha='right')
            ax.grid(True, alpha=0.3)
            
            # 在柱子上显示数值
            for bar, map_val in zip(bars, maps):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{map_val:.3f}', ha='center', va='bottom')
                       
            # 添加目标线
            ax.axhline(y=0.9, color='red', linestyle='--', alpha=0.7, label='目标准确率 (90%)')
            ax.legend()
            
            plt.tight_layout()
            plt.savefig(save_dir / 'class_performance.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        # 2. 整体性能雷达图
        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        
        metrics = ['mAP50', 'mAP50-95', '精确率', '召回率']
        values = [
            results.box.map50,
            results.box.map,
            results.box.mp,
            results.box.mr
        ]
        
        # 闭合雷达图
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        values += values[:1]
        angles += angles[:1]
        
        ax.plot(angles, values, 'o-', linewidth=2, color='blue', alpha=0.7)
        ax.fill(angles, values, alpha=0.25, color='blue')
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metrics)
        ax.set_ylim(0, 1)
        ax.set_title('模型整体性能雷达图', pad=20)
        ax.grid(True)
        
        plt.savefig(save_dir / 'performance_radar.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"性能图表已保存到: {save_dir}")
        
    def test_on_field_images(self, field_images_dir, save_dir=None):
        """
        在场地图片上测试模型
        
        Args:
            field_images_dir: 场地图片目录
            save_dir: 保存结果的目录
        """
        field_path = Path(field_images_dir)
        if not field_path.exists():
            raise FileNotFoundError(f"场地图片目录不存在: {field_path}")
            
        if save_dir is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_dir = self.project_root / 'runs' / 'field_test' / f"test_{timestamp}"
        else:
            save_dir = Path(save_dir)
            
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        
        # 获取所有图片文件
        image_files = [f for f in field_path.iterdir() 
                      if f.suffix.lower() in image_extensions]
        
        if not image_files:
            print(f"在目录 {field_path} 中未找到图片文件")
            return
            
        print(f"在场地图片上测试模型，共 {len(image_files)} 张图片...")
        
        # 统计信息
        detection_stats = {name: 0 for name in self.class_names}
        confidence_stats = {name: [] for name in self.class_names}
        
        for i, image_file in enumerate(image_files):
            print(f"测试 ({i+1}/{len(image_files)}): {image_file.name}")
            
            # 进行预测
            results = self.model(
                str(image_file),
                conf=0.5,
                iou=0.45,
                device=self.device,
                save=True,
                project=str(save_dir),
                name='predictions'
            )
            
            # 统计检测结果
            if len(results) > 0 and results[0].boxes is not None:
                boxes = results[0].boxes
                for j in range(len(boxes)):
                    class_id = int(boxes.cls[j].cpu().numpy())
                    confidence = float(boxes.conf[j].cpu().numpy())
                    class_name = self.class_names[class_id]
                    
                    detection_stats[class_name] += 1
                    confidence_stats[class_name].append(confidence)
                    
        # 生成场地测试报告
        self.generate_field_test_report(detection_stats, confidence_stats, save_dir)
        
    def generate_field_test_report(self, detection_stats, confidence_stats, save_dir):
        """
        生成场地测试报告
        
        Args:
            detection_stats: 检测统计
            confidence_stats: 置信度统计
            save_dir: 保存目录
        """
        report_file = save_dir / "field_test_report.txt"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("场地图片测试报告\n")
            f.write("=" * 50 + "\n\n")
            
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"模型路径: {self.model_path}\n\n")
            
            total_detections = sum(detection_stats.values())
            f.write(f"总检测数量: {total_detections}\n\n")
            
            f.write("各类别检测统计:\n")
            f.write("-" * 30 + "\n")
            
            for class_name, count in detection_stats.items():
                if count > 0:
                    chinese_name = self.chinese_names.get(class_name, class_name)
                    avg_conf = np.mean(confidence_stats[class_name])
                    f.write(f"{chinese_name}({class_name}):\n")
                    f.write(f"  检测数量: {count}\n")
                    f.write(f"  平均置信度: {avg_conf:.3f}\n")
                    f.write(f"  置信度范围: {min(confidence_stats[class_name]):.3f} - {max(confidence_stats[class_name]):.3f}\n\n")
                    
        print(f"场地测试报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description='YOLO模型评估脚本')
    parser.add_argument('--model', type=str, required=True, help='训练好的模型路径')
    parser.add_argument('--config', type=str, default='data/data.yaml', help='数据配置文件')
    parser.add_argument('--save-dir', type=str, help='保存评估结果的目录')
    parser.add_argument('--field-test', type=str, help='场地图片目录，用于实际场景测试')
    
    args = parser.parse_args()
    
    try:
        # 创建评估器
        evaluator = YOLOEvaluator(
            model_path=args.model,
            config_path=args.config
        )
        
        # 评估模型
        results = evaluator.evaluate(save_dir=args.save_dir)
        
        # 场地测试
        if args.field_test:
            evaluator.test_on_field_images(args.field_test)
            
        print("评估完成!")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
