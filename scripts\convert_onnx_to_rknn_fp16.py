#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX模型转换为RKNN模型脚本 - 全精度FP16版本
支持Linux仿真模式，避免量化问题
"""

import os
import sys
import argparse
from pathlib import Path

def check_rknn_toolkit():
    """检查RKNN-Toolkit2是否安装"""
    try:
        from rknn.api import RKNN
        print("✅ RKNN-Toolkit2 已安装")
        return True
    except ImportError:
        print("❌ RKNN-Toolkit2 未安装")
        print("请安装RKNN-Toolkit2:")
        print("pip install rknn-toolkit2")
        return False

def convert_onnx_to_rknn_fp16(onnx_path, output_path, target_platform='rk3588', verbose=True):
    """
    将ONNX模型转换为RKNN模型 - 全精度FP16版本
    
    Args:
        onnx_path: ONNX模型路径
        output_path: 输出RKNN模型路径
        target_platform: 目标平台
        verbose: 是否显示详细信息
    """
    from rknn.api import RKNN
    
    # 创建RKNN对象
    rknn = RKNN(verbose=verbose)
    
    try:
        print(f"🔧 开始转换ONNX模型: {onnx_path}")
        print(f"🎯 目标平台: {target_platform}")
        print(f"📊 精度模式: FP16 (全精度，无量化)")
        
        # 配置RKNN参数
        print("⚙️  配置RKNN参数...")
        print("📝 注意：配置为RGB通道顺序，与ONNX模型保持一致")
        ret = rknn.config(
            mean_values=[[0, 0, 0]],           # 图像均值 (RGB顺序)
            std_values=[[255, 255, 255]],      # 图像标准差 (RGB顺序)
            target_platform=target_platform,   # 目标平台
            # 确保使用RGB通道顺序，与ONNX训练时一致
        )
        
        if ret != 0:
            print("❌ RKNN配置失败")
            return False
        
        # 加载ONNX模型
        print("📦 加载ONNX模型...")
        ret = rknn.load_onnx(model=onnx_path)
        if ret != 0:
            print("❌ 加载ONNX模型失败")
            return False
        
        print("✅ ONNX模型加载成功")
        
        # 构建RKNN模型 - 使用FP16精度
        print("🔧 构建RKNN模型 (FP16精度)...")
        ret = rknn.build(
            do_quantization=False,    # 不进行量化，保持FP16精度
            rknn_batch_size=1,        # 批次大小
            # 不使用量化数据集
        )
        
        if ret != 0:
            print("❌ 构建RKNN模型失败")
            return False
        
        print("✅ RKNN模型构建成功")
        
        # 导出RKNN模型
        print(f"💾 导出RKNN模型到: {output_path}")
        ret = rknn.export_rknn(output_path)
        if ret != 0:
            print("❌ 导出RKNN模型失败")
            return False
        
        print("✅ RKNN模型导出成功")
        
        # 获取模型信息
        model_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
        print(f"📊 模型大小: {model_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 释放资源
        rknn.release()

def test_rknn_simulation(rknn_path, test_image_path=None):
    """
    测试RKNN模型仿真功能
    
    Args:
        rknn_path: RKNN模型路径
        test_image_path: 测试图像路径（可选）
    """
    from rknn.api import RKNN
    import numpy as np
    
    print(f"\n🧪 测试RKNN模型仿真: {rknn_path}")
    
    rknn = RKNN(verbose=False)
    
    try:
        # 加载RKNN模型
        ret = rknn.load_rknn(rknn_path)
        if ret != 0:
            print("❌ 加载RKNN模型失败")
            return False
        
        # 初始化仿真运行时
        print("🔧 初始化仿真运行时...")
        ret = rknn.init_runtime()  # 不指定target，自动使用仿真模式
        if ret != 0:
            print("❌ 初始化仿真运行时失败")
            return False
        
        print("✅ 仿真运行时初始化成功")
        
        # 创建测试输入
        if test_image_path and os.path.exists(test_image_path):
            print(f"📸 使用测试图像: {test_image_path}")
            # 这里可以添加实际的图像预处理代码
            test_input = np.random.randint(0, 255, (1, 640, 640, 3), dtype=np.uint8)
        else:
            print("🎲 使用随机测试数据")
            test_input = np.random.randint(0, 255, (1, 640, 640, 3), dtype=np.uint8)
        
        # 执行推理测试
        print("⚡ 执行推理测试...")
        outputs = rknn.inference(inputs=[test_input], data_format=['nhwc'])
        
        if outputs is None or len(outputs) == 0:
            print("❌ 推理测试失败")
            return False
        
        print("✅ 推理测试成功")
        print(f"📊 输出形状: {[output.shape for output in outputs]}")
        print(f"📊 输出数据类型: {[output.dtype for output in outputs]}")
        
        return True
        
    except Exception as e:
        print(f"❌ 仿真测试出错: {e}")
        return False
    
    finally:
        rknn.release()

def main():
    parser = argparse.ArgumentParser(description='ONNX转RKNN模型 - FP16全精度版本')
    parser.add_argument('--onnx', type=str, required=True, help='ONNX模型路径')
    parser.add_argument('--output', type=str, help='输出RKNN模型路径')
    parser.add_argument('--target', type=str, default='rk3588', 
                       choices=['rk3566', 'rk3568', 'rk3588', 'rv1103', 'rv1106', 'rk3562', 'rk3576'],
                       help='目标平台')
    parser.add_argument('--test', action='store_true', help='转换后测试仿真功能')
    parser.add_argument('--test-image', type=str, help='测试图像路径')
    
    args = parser.parse_args()
    
    print("🚀 ONNX转RKNN模型转换器 - FP16全精度版本")
    print("="*80)
    
    # 检查RKNN-Toolkit2
    if not check_rknn_toolkit():
        return
    
    # 检查输入文件
    if not os.path.exists(args.onnx):
        print(f"❌ ONNX模型文件不存在: {args.onnx}")
        return
    
    # 确定输出路径
    if args.output:
        output_path = args.output
    else:
        onnx_path = Path(args.onnx)
        output_path = onnx_path.parent / f"{onnx_path.stem}_{args.target}_fp16.rknn"
    
    # 创建输出目录
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    print(f"📁 输入文件: {args.onnx}")
    print(f"📁 输出文件: {output_path}")
    print(f"🎯 目标平台: {args.target}")
    print("="*80)
    
    # 执行转换
    success = convert_onnx_to_rknn_fp16(
        onnx_path=args.onnx,
        output_path=output_path,
        target_platform=args.target,
        verbose=True
    )
    
    if not success:
        print("❌ 转换失败")
        return
    
    print("\n" + "="*80)
    print("🎉 转换完成!")
    print(f"📁 RKNN模型已保存到: {output_path}")
    
    # 测试仿真功能
    if args.test:
        print("\n" + "="*80)
        test_success = test_rknn_simulation(output_path, args.test_image)
        if test_success:
            print("🎉 仿真测试通过!")
        else:
            print("❌ 仿真测试失败")
    
    print("\n💡 使用说明:")
    print("1. 这个模型使用FP16精度，没有量化，精度更高")
    print("2. 可以在Linux上直接仿真运行")
    print("3. 部署到RK3588时会自动使用NPU加速")
    print("4. 模型大小可能比量化版本大，但精度更好")

if __name__ == '__main__':
    main()
