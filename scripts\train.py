#!/usr/bin/env python3
"""
YOLO训练脚本
支持多类别物体识别：苹果，香蕉，西瓜，土豆，西红柿，彩椒，牛奶，可乐，蛋糕，红灯，绿灯
目标准确率：90%以上
"""

import os
import sys
import yaml
import torch
from pathlib import Path
from ultralytics import YOLO
import argparse
from datetime import datetime

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class YOLOTrainer:
    def __init__(self, config_path="data/data.yaml", model_path="scripts/model/yolo11n.pt"):
        """
        初始化YOLO训练器
        
        Args:
            config_path: 数据配置文件路径
            model_path: 预训练模型路径
        """
        self.project_root = project_root
        self.config_path = self.project_root / config_path
        self.model_path = self.project_root / model_path
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 验证文件存在
        if not self.config_path.exists():
            raise FileNotFoundError(f"数据配置文件不存在: {self.config_path}")
        if not self.model_path.exists():
            raise FileNotFoundError(f"预训练模型不存在: {self.model_path}")
            
        print(f"使用设备: {self.device}")
        print(f"数据配置: {self.config_path}")
        print(f"预训练模型: {self.model_path}")
        
    def load_data_config(self):
        """加载数据配置"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # 验证类别数量和名称
        expected_classes = ['apple', 'banana', 'watermelon', 'potato', 'tomato', 
                          'chili', 'milk', 'cola', 'cake', 'redlight', 'greenlight']
        
        print(f"数据集类别数: {config['nc']}")
        print(f"数据集类别: {config['names']}")
        
        # 检查是否包含所需类别
        missing_classes = set(expected_classes) - set(config['names'])
        if missing_classes:
            print(f"警告: 缺少类别 {missing_classes}")
            
        return config
        
    def train(self, epochs=100, batch_size=16, img_size=640, patience=50, 
              save_period=10, project_name=None):
        """
        训练YOLO模型
        
        Args:
            epochs: 训练轮数
            batch_size: 批次大小
            img_size: 输入图像尺寸
            patience: 早停耐心值
            save_period: 保存周期
            project_name: 项目名称
        """
        # 加载数据配置
        data_config = self.load_data_config()
        
        # 创建模型
        model = YOLO(str(self.model_path))
        
        # 设置项目名称
        if project_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            project_name = f"yolo11n_11classes_{timestamp}"
            
        # 训练参数
        train_args = {
            'data': str(self.config_path),
            'epochs': epochs,
            'batch': batch_size,
            'imgsz': img_size,
            'device': self.device,
            'project': str(self.project_root / 'runs' / 'train'),
            'name': project_name,
            'patience': patience,
            'save_period': save_period,
            'verbose': True,
            'val': True,
            'plots': True,
            'save': True,
            'cache': False,  # 如果内存足够可以设为True
            'workers': 8,
            'optimizer': 'SGD',
            'lr0': 0.01,
            'lrf': 0.01,
            'momentum': 0.937,
            'weight_decay': 0.0005,
            'warmup_epochs': 3,
            'warmup_momentum': 0.8,
            'warmup_bias_lr': 0.1,
            'box': 7.5,
            'cls': 0.5,
            'dfl': 1.5,
            'hsv_h': 0.015,
            'hsv_s': 0.7,
            'hsv_v': 0.4,
            'degrees': 15.0,
            'translate': 0.1,
            'scale': 0.5,
            'shear': 2.0,
            'perspective': 0.0,
            'flipud': 0.0,
            'fliplr': 0.5,
            'mosaic': 1.0,
            'mixup': 0.1,
            'copy_paste': 0.1,
            'auto_augment': 'randaugment',
            'erasing': 0.4,
            'crop_fraction': 1.0,
        }
        
        print(f"开始训练模型: {project_name}")
        print(f"训练参数: {train_args}")
        
        # 开始训练
        try:
            results = model.train(**train_args)
            
            # 训练完成后的信息
            print(f"\n训练完成!")
            print(f"最佳权重保存在: {results.save_dir}/weights/best.pt")
            print(f"最后权重保存在: {results.save_dir}/weights/last.pt")
            
            # 验证最佳模型
            best_model = YOLO(f"{results.save_dir}/weights/best.pt")
            val_results = best_model.val()
            
            print(f"\n验证结果:")
            print(f"mAP50: {val_results.box.map50:.4f}")
            print(f"mAP50-95: {val_results.box.map:.4f}")
            
            # 检查是否达到目标准确率
            if val_results.box.map50 >= 0.90:
                print(f"✅ 达到目标准确率! mAP50: {val_results.box.map50:.4f} >= 0.90")
            else:
                print(f"⚠️  未达到目标准确率. mAP50: {val_results.box.map50:.4f} < 0.90")
                print("建议: 增加训练轮数、调整学习率或增加数据增强")
                
            return results, val_results
            
        except Exception as e:
            print(f"训练过程中出现错误: {e}")
            raise
            
    def resume_training(self, checkpoint_path, epochs=50):
        """
        从检查点恢复训练
        
        Args:
            checkpoint_path: 检查点文件路径
            epochs: 额外训练轮数
        """
        if not Path(checkpoint_path).exists():
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")
            
        model = YOLO(checkpoint_path)
        
        print(f"从检查点恢复训练: {checkpoint_path}")
        
        results = model.train(
            data=str(self.config_path),
            epochs=epochs,
            resume=True,
            device=self.device
        )
        
        return results

def main():
    parser = argparse.ArgumentParser(description='YOLO模型训练脚本')
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--img-size', type=int, default=640, help='输入图像尺寸')
    parser.add_argument('--patience', type=int, default=50, help='早停耐心值')
    parser.add_argument('--project-name', type=str, help='项目名称')
    parser.add_argument('--resume', type=str, help='从检查点恢复训练')
    parser.add_argument('--config', type=str, default='data/data.yaml', help='数据配置文件')
    parser.add_argument('--model', type=str, default='scripts/model/yolo11n.pt', help='预训练模型')
    
    args = parser.parse_args()
    
    try:
        # 创建训练器
        trainer = YOLOTrainer(config_path=args.config, model_path=args.model)
        
        if args.resume:
            # 恢复训练
            results = trainer.resume_training(args.resume, args.epochs)
        else:
            # 新训练
            results, val_results = trainer.train(
                epochs=args.epochs,
                batch_size=args.batch_size,
                img_size=args.img_size,
                patience=args.patience,
                project_name=args.project_name
            )
            
        print("训练脚本执行完成!")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
