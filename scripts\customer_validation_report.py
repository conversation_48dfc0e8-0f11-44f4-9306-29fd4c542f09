#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
客户验证报告生成脚本
生成详细的模型验证报告，用于与客户对比结果
"""

import os
import cv2
import numpy as np
import json
import time
from pathlib import Path
import argparse
from datetime import datetime

# 类别映射
CLASS_NAMES = {
    0: 'apple', 1: 'banana', 2: 'board', 3: 'cake', 4: 'chili', 5: 'cola',
    6: 'greenlight', 7: 'milk', 8: 'potato', 9: 'redlight', 10: 'tomato', 11: 'watermelon'
}

CLASS_NAMES_CN = {
    0: '苹果', 1: '香蕉', 2: '板子', 3: '蛋糕', 4: '彩椒', 5: '可乐',
    6: '绿灯', 7: '牛奶', 8: '土豆', 9: '红灯', 10: '西红柿', 11: '西瓜'
}

def run_onnx_inference(image_path, conf_threshold=0.25):
    """使用ONNX模型进行推理（作为基准）"""
    try:
        import onnxruntime as ort
        
        # 加载ONNX模型
        onnx_path = "models/best.onnx"
        if not os.path.exists(onnx_path):
            return None, "ONNX模型不存在"
        
        session = ort.InferenceSession(onnx_path, providers=['CPUExecutionProvider'])
        
        # 预处理图像
        img_array = np.fromfile(image_path, dtype=np.uint8)
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        if img is None:
            return None, "图像读取失败"
        
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        h, w = img_rgb.shape[:2]
        
        # 缩放和填充
        scale = min(640 / h, 640 / w)
        new_h, new_w = int(h * scale), int(w * scale)
        img_resized = cv2.resize(img_rgb, (new_w, new_h))
        
        padded_img = np.full((640, 640, 3), 114, dtype=np.uint8)
        top = (640 - new_h) // 2
        left = (640 - new_w) // 2
        padded_img[top:top+new_h, left:left+new_w] = img_resized
        
        # 转换为NCHW格式并归一化
        input_data = padded_img.transpose(2, 0, 1).astype(np.float32) / 255.0
        input_data = np.expand_dims(input_data, axis=0)
        
        # 推理
        start_time = time.time()
        outputs = session.run(None, {'images': input_data})
        inference_time = time.time() - start_time
        
        # 后处理
        output = outputs[0][0].T  # (8400, 16)
        boxes = output[:, :4]
        scores = output[:, 4:]
        
        max_scores = np.max(scores, axis=1)
        class_ids = np.argmax(scores, axis=1)
        
        valid_indices = max_scores >= conf_threshold
        if not np.any(valid_indices):
            return [], f"ONNX推理成功，耗时{inference_time*1000:.2f}ms，无检测结果"
        
        boxes = boxes[valid_indices]
        max_scores = max_scores[valid_indices]
        class_ids = class_ids[valid_indices]
        
        # 转换边界框格式
        x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        boxes = np.column_stack([x1, y1, x2, y2])
        
        # NMS
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), max_scores.tolist(), conf_threshold, 0.45)
        
        detections = []
        if len(indices) > 0:
            indices = indices.flatten()
            for i in indices:
                x1, y1, x2, y2 = boxes[i]
                conf = max_scores[i]
                class_id = class_ids[i]
                
                if class_id == 2:  # 过滤board类别
                    continue
                
                if class_id not in CLASS_NAMES:
                    continue
                
                # 缩放回原始图像尺寸
                x1 = (x1 - (640 - w * scale) / 2) / scale
                y1 = (y1 - (640 - h * scale) / 2) / scale
                x2 = (x2 - (640 - w * scale) / 2) / scale
                y2 = (y2 - (640 - h * scale) / 2) / scale
                
                x1 = max(0, min(x1, w))
                y1 = max(0, min(y1, h))
                x2 = max(0, min(x2, w))
                y2 = max(0, min(y2, h))
                
                detections.append({
                    'class_id': int(class_id),
                    'class_name': CLASS_NAMES[class_id],
                    'class_name_cn': CLASS_NAMES_CN[class_id],
                    'confidence': float(conf),
                    'bbox': [int(x1), int(y1), int(x2), int(y2)]
                })
        
        return detections, f"ONNX推理成功，耗时{inference_time*1000:.2f}ms"
        
    except Exception as e:
        return None, f"ONNX推理失败: {e}"

def generate_validation_report(image_dir, output_dir, max_images=10):
    """生成客户验证报告"""
    
    print("🚀 生成客户验证报告")
    print("="*80)
    
    # 创建输出目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_dir = os.path.join(output_dir, f"validation_report_{timestamp}")
    os.makedirs(report_dir, exist_ok=True)
    
    # 获取图片列表
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    image_files = []
    for ext in image_extensions:
        image_files.extend(Path(image_dir).glob(f'*{ext}'))
        image_files.extend(Path(image_dir).glob(f'*{ext.upper()}'))
    
    if len(image_files) == 0:
        print(f"❌ 在 {image_dir} 中未找到图片文件")
        return
    
    image_files = image_files[:max_images]
    print(f"📁 找到 {len(image_files)} 张图片进行验证")
    
    # 验证结果
    validation_results = []
    total_detections = 0
    class_counts = {}
    
    for i, image_path in enumerate(image_files, 1):
        print(f"\n[{i}/{len(image_files)}] 验证: {Path(image_path).name}")
        print("-" * 60)
        
        # ONNX推理（作为基准）
        detections, message = run_onnx_inference(str(image_path))
        print(f"📊 {message}")
        
        if detections is not None:
            print(f"🎯 检测到 {len(detections)} 个物体:")
            for det in detections:
                print(f"   - {det['class_name_cn']}: {det['confidence']:.3f}")
                
                # 统计类别
                class_name = det['class_name_cn']
                if class_name in class_counts:
                    class_counts[class_name] += 1
                else:
                    class_counts[class_name] = 1
            
            total_detections += len(detections)
        
        # 保存结果
        validation_results.append({
            'image_name': Path(image_path).name,
            'image_path': str(image_path),
            'detections': detections if detections is not None else [],
            'message': message,
            'success': detections is not None
        })
    
    # 生成报告
    report = {
        'report_info': {
            'timestamp': timestamp,
            'total_images': len(image_files),
            'total_detections': total_detections,
            'average_detections': total_detections / len(image_files) if image_files else 0,
            'model_info': {
                'type': 'YOLO11 Object Detection',
                'classes': list(CLASS_NAMES_CN.values()),
                'input_size': '640x640',
                'precision': 'FP16'
            }
        },
        'class_statistics': class_counts,
        'validation_results': validation_results
    }
    
    # 保存JSON报告
    json_report_path = os.path.join(report_dir, 'validation_report.json')
    with open(json_report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    # 生成客户对比文档
    customer_report_path = os.path.join(report_dir, '客户对比验证报告.txt')
    with open(customer_report_path, 'w', encoding='utf-8') as f:
        f.write("YOLO11 模型验证报告\n")
        f.write("="*50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"验证图片: {len(image_files)} 张\n")
        f.write(f"总检测数: {total_detections}\n")
        f.write(f"平均检测数: {total_detections/len(image_files):.1f}\n\n")
        
        f.write("类别统计:\n")
        f.write("-" * 30 + "\n")
        for class_name, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_detections * 100 if total_detections > 0 else 0
            f.write(f"{class_name}: {count} 次 ({percentage:.1f}%)\n")
        
        f.write("\n详细检测结果:\n")
        f.write("-" * 30 + "\n")
        for result in validation_results:
            f.write(f"\n图片: {result['image_name']}\n")
            if result['success'] and result['detections']:
                for det in result['detections']:
                    f.write(f"  - {det['class_name_cn']}: {det['confidence']:.3f} "
                           f"位置: ({det['bbox'][0]}, {det['bbox'][1]}, {det['bbox'][2]}, {det['bbox'][3]})\n")
            else:
                f.write(f"  无检测结果\n")
        
        f.write(f"\n客户对比说明:\n")
        f.write("-" * 30 + "\n")
        f.write("1. 请客户使用相同的图片在RK3588设备上运行模型\n")
        f.write("2. 对比检测的物体类别和置信度\n")
        f.write("3. 如果结果不一致，可能的原因:\n")
        f.write("   - 客户使用的模型版本不同\n")
        f.write("   - 图片预处理方式不同\n")
        f.write("   - 置信度阈值设置不同\n")
        f.write("   - RK3588环境配置问题\n")
        f.write("4. 建议客户提供具体的检测结果进行对比\n")
    
    print(f"\n" + "="*80)
    print("📊 验证报告生成完成")
    print("="*80)
    print(f"总验证图片: {len(image_files)}")
    print(f"总检测数: {total_detections}")
    print(f"平均检测数: {total_detections/len(image_files):.1f}")
    
    print(f"\n📈 类别统计:")
    for class_name, count in sorted(class_counts.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total_detections * 100 if total_detections > 0 else 0
        print(f"   {class_name}: {count} 次 ({percentage:.1f}%)")
    
    print(f"\n📁 报告文件:")
    print(f"   JSON报告: {json_report_path}")
    print(f"   客户对比: {customer_report_path}")
    
    print(f"\n💡 使用说明:")
    print("1. 将客户对比报告发送给客户")
    print("2. 要求客户使用相同图片测试并提供结果")
    print("3. 对比检测结果找出差异原因")
    print("4. 这个报告基于ONNX模型，与RKNN模型应该完全一致")

def main():
    parser = argparse.ArgumentParser(description='生成客户验证报告')
    parser.add_argument('--images', type=str, default='image', help='图片目录')
    parser.add_argument('--output', type=str, default='validation_reports', help='输出目录')
    parser.add_argument('--max-images', type=int, default=10, help='最大验证图片数量')
    
    args = parser.parse_args()
    
    generate_validation_report(args.images, args.output, args.max_images)

if __name__ == '__main__':
    main()
