#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX模型实时摄像头推理脚本 - 科技感UI版本
支持摄像头实时检测，具有科技感的四角边线框和蒙版效果
"""

import cv2
import numpy as np
import time
import argparse
import os
import onnxruntime as ort
from collections import deque

# 类别名称映射
CLASS_NAMES = {
    0: 'apple', 1: 'banana', 2: 'board', 3: 'cake', 4: 'chili', 5: 'cola',
    6: 'greenlight', 7: 'milk', 8: 'potato', 9: 'redlight', 10: 'tomato', 11: 'watermelon'
}

# 类别颜色映射 (BGR格式) - 科技感配色
CLASS_COLORS = {
    0: (0, 255, 100),    # apple - 青绿色
    1: (0, 200, 255),    # banana - 橙黄色
    2: (128, 128, 128),  # board - 灰色
    3: (255, 100, 255),  # cake - 紫红色
    4: (0, 100, 255),    # chili - 橙红色
    5: (100, 50, 200),   # cola - 深紫色
    6: (0, 255, 0),      # greenlight - 纯绿色
    7: (255, 255, 255),  # milk - 白色
    8: (0, 150, 255),    # potato - 橙色
    9: (0, 50, 255),     # redlight - 红色
    10: (0, 100, 255),   # tomato - 橙红色
    11: (100, 255, 100), # watermelon - 浅绿色
}

class RealTimeONNXInference:
    def __init__(self, model_path, conf_threshold=0.25, nms_threshold=0.45, target_size=640):
        """
        初始化实时ONNX推理器
        
        Args:
            model_path: ONNX模型路径
            conf_threshold: 置信度阈值
            nms_threshold: NMS阈值
            target_size: 输入图像尺寸
        """
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.nms_threshold = nms_threshold
        self.target_size = target_size
        self.session = None
        
        # 性能统计
        self.fps_queue = deque(maxlen=30)  # 保存最近30帧的FPS
        self.inference_times = deque(maxlen=30)  # 保存最近30次推理时间
        
        # 初始化ONNX模型
        self._init_onnx()
    
    def _init_onnx(self):
        """初始化ONNX模型"""
        try:
            print(f"🔧 加载ONNX模型: {self.model_path}")
            
            # 配置ONNX Runtime
            providers = ['CPUExecutionProvider']
            if ort.get_available_providers():
                available = ort.get_available_providers()
                if 'CUDAExecutionProvider' in available:
                    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
                    print("🚀 使用CUDA加速")
                else:
                    print("💻 使用CPU推理")
            
            self.session = ort.InferenceSession(self.model_path, providers=providers)
            
            # 获取输入输出信息
            self.input_name = self.session.get_inputs()[0].name
            self.output_names = [output.name for output in self.session.get_outputs()]
            
            print("✅ ONNX模型初始化成功")
            print(f"📝 输入名称: {self.input_name}")
            print(f"📝 输出名称: {self.output_names}")
            
        except Exception as e:
            raise RuntimeError(f"❌ ONNX初始化失败: {e}")
    
    def preprocess_frame(self, frame):
        """
        预处理摄像头帧
        
        Args:
            frame: 摄像头帧 (BGR格式)
            
        Returns:
            input_data: 预处理后的输入数据
            scale: 缩放比例
            offset: 偏移量
            original_size: 原始尺寸
        """
        original_h, original_w = frame.shape[:2]
        
        # 计算缩放比例
        scale = min(self.target_size / original_h, self.target_size / original_w)
        new_h, new_w = int(original_h * scale), int(original_w * scale)
        
        # 缩放图像
        img_resized = cv2.resize(frame, (new_w, new_h))
        
        # 创建填充图像
        padded_img = np.full((self.target_size, self.target_size, 3), 114, dtype=np.uint8)
        top = (self.target_size - new_h) // 2
        left = (self.target_size - new_w) // 2
        padded_img[top:top+new_h, left:left+new_w] = img_resized
        
        # 转换为NCHW格式并归一化 (ONNX格式)
        img_rgb = cv2.cvtColor(padded_img, cv2.COLOR_BGR2RGB)
        input_data = img_rgb.transpose(2, 0, 1).astype(np.float32) / 255.0
        input_data = np.expand_dims(input_data, axis=0)
        
        return input_data, scale, (top, left), (original_h, original_w)
    
    def postprocess_detections(self, output, scale, offset, original_size):
        """
        后处理检测结果
        
        Args:
            output: 模型输出
            scale: 缩放比例
            offset: 偏移量
            original_size: 原始尺寸
            
        Returns:
            detections: 检测结果列表
        """
        if len(output.shape) == 3:
            output = output[0]
        
        if output.shape[0] == 16:
            output = output.T
        
        boxes = output[:, :4]
        scores = output[:, 4:]
        
        max_scores = np.max(scores, axis=1)
        class_ids = np.argmax(scores, axis=1)
        
        # 置信度过滤
        valid_indices = max_scores >= self.conf_threshold
        if not np.any(valid_indices):
            return []
        
        boxes = boxes[valid_indices]
        max_scores = max_scores[valid_indices]
        class_ids = class_ids[valid_indices]
        
        # 转换边界框格式 (center_x, center_y, width, height) -> (x1, y1, x2, y2)
        x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
        x1 = x_center - width / 2
        y1 = y_center - height / 2
        x2 = x_center + width / 2
        y2 = y_center + height / 2
        boxes = np.column_stack([x1, y1, x2, y2])
        
        # NMS
        indices = cv2.dnn.NMSBoxes(boxes.tolist(), max_scores.tolist(), 
                                  self.conf_threshold, self.nms_threshold)
        
        detections = []
        if len(indices) > 0:
            indices = indices.flatten()
            for i in indices:
                x1, y1, x2, y2 = boxes[i]
                conf = max_scores[i]
                class_id = class_ids[i]
                
                # 过滤board类别
                if class_id == 2:
                    continue
                
                if class_id not in CLASS_NAMES:
                    continue
                
                # 坐标转换回原始图像
                top, left = offset
                original_h, original_w = original_size
                
                x1 = (x1 - left) / scale
                y1 = (y1 - top) / scale
                x2 = (x2 - left) / scale
                y2 = (y2 - top) / scale
                
                # 边界检查
                x1 = max(0, min(x1, original_w))
                y1 = max(0, min(y1, original_h))
                x2 = max(0, min(x2, original_w))
                y2 = max(0, min(y2, original_h))
                
                detections.append({
                    'class_id': int(class_id),
                    'class_name': CLASS_NAMES[class_id],
                    'confidence': float(conf),
                    'bbox': [float(x1), float(y1), float(x2), float(y2)]
                })
        
        return detections
    
    def inference(self, frame):
        """
        对单帧进行推理
        
        Args:
            frame: 输入帧
            
        Returns:
            detections: 检测结果
            inference_time: 推理时间(ms)
        """
        # 预处理
        input_data, scale, offset, original_size = self.preprocess_frame(frame)
        
        # 推理
        start_time = time.time()
        outputs = self.session.run(self.output_names, {self.input_name: input_data})
        inference_time = (time.time() - start_time) * 1000  # 转换为毫秒
        
        # 后处理
        detections = self.postprocess_detections(outputs[0], scale, offset, original_size)
        
        # 更新性能统计
        self.inference_times.append(inference_time)
        
        return detections, inference_time

    def draw_corner_box(self, frame, x1, y1, x2, y2, color, thickness=2, corner_length=20):
        """
        绘制科技感四角边线框

        Args:
            frame: 图像帧
            x1, y1, x2, y2: 边界框坐标
            color: 颜色
            thickness: 线条粗细
            corner_length: 角线长度
        """
        # 左上角
        cv2.line(frame, (x1, y1), (x1 + corner_length, y1), color, thickness)
        cv2.line(frame, (x1, y1), (x1, y1 + corner_length), color, thickness)

        # 右上角
        cv2.line(frame, (x2, y1), (x2 - corner_length, y1), color, thickness)
        cv2.line(frame, (x2, y1), (x2, y1 + corner_length), color, thickness)

        # 左下角
        cv2.line(frame, (x1, y2), (x1 + corner_length, y2), color, thickness)
        cv2.line(frame, (x1, y2), (x1, y2 - corner_length), color, thickness)

        # 右下角
        cv2.line(frame, (x2, y2), (x2 - corner_length, y2), color, thickness)
        cv2.line(frame, (x2, y2), (x2, y2 - corner_length), color, thickness)

    def draw_detections(self, frame, detections, inference_time):
        """
        在帧上绘制科技感检测结果

        Args:
            frame: 输入帧
            detections: 检测结果
            inference_time: 推理时间

        Returns:
            annotated_frame: 标注后的帧
        """
        annotated_frame = frame.copy()

        # 绘制检测框
        for det in detections:
            x1, y1, x2, y2 = [int(coord) for coord in det['bbox']]
            class_id = det['class_id']
            class_name = det['class_name']
            confidence = det['confidence']

            # 获取颜色
            color = CLASS_COLORS.get(class_id, (0, 255, 255))

            # 绘制淡色蒙版
            mask_color = tuple(int(c * 0.3) for c in color)  # 30%透明度
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (x1, y1), (x2, y2), mask_color, -1)
            cv2.addWeighted(annotated_frame, 0.8, overlay, 0.2, 0, annotated_frame)

            # 绘制四角边线框
            corner_length = min(30, (x2-x1)//4, (y2-y1)//4)
            self.draw_corner_box(annotated_frame, x1, y1, x2, y2, color, 3, corner_length)

            # 绘制标签背景
            label = f"{class_name} {confidence:.2f}"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            font_thickness = 2

            # 计算文字尺寸
            (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, font_thickness)

            # 标签位置（在框的上方）
            label_x = x1
            label_y = y1 - 10

            # 如果标签超出图像顶部，放在框内部
            if label_y - text_height < 0:
                label_y = y1 + text_height + 10

            # 绘制标签背景（半透明黑色）
            bg_x1 = label_x - 5
            bg_y1 = label_y - text_height - 5
            bg_x2 = label_x + text_width + 5
            bg_y2 = label_y + 5

            # 创建标签背景
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (bg_x1, bg_y1), (bg_x2, bg_y2), (0, 0, 0), -1)
            cv2.addWeighted(annotated_frame, 0.7, overlay, 0.3, 0, annotated_frame)

            # 绘制标签边框
            cv2.rectangle(annotated_frame, (bg_x1, bg_y1), (bg_x2, bg_y2), color, 1)

            # 绘制标签文字
            cv2.putText(annotated_frame, label, (label_x, label_y), font, font_scale, color, font_thickness)

        # 绘制性能信息
        self._draw_performance_info(annotated_frame, inference_time, len(detections))

        return annotated_frame

    def _draw_performance_info(self, frame, inference_time, detection_count):
        """绘制科技感性能信息"""
        h, w = frame.shape[:2]

        # 计算平均推理时间和FPS
        avg_inference_time = np.mean(self.inference_times) if self.inference_times else 0
        fps = 1000 / avg_inference_time if avg_inference_time > 0 else 0

        # 性能文本 - 简洁版本
        fps_text = f"FPS: {fps:.1f}"

        # 绘制FPS信息（右上角）
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        font_thickness = 2

        # 计算文字尺寸
        (text_width, text_height), baseline = cv2.getTextSize(fps_text, font, font_scale, font_thickness)

        # FPS位置（右上角）
        fps_x = w - text_width - 20
        fps_y = 30

        # 绘制FPS背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (fps_x - 10, fps_y - text_height - 5),
                     (fps_x + text_width + 10, fps_y + 5), (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)

        # 绘制FPS边框和文字
        cv2.rectangle(frame, (fps_x - 10, fps_y - text_height - 5),
                     (fps_x + text_width + 10, fps_y + 5), (0, 255, 255), 1)
        cv2.putText(frame, fps_text, (fps_x, fps_y), font, font_scale, (0, 255, 255), font_thickness)

        # 绘制检测数量（左上角，小字）
        if detection_count > 0:
            det_text = f"Objects: {detection_count}"
            det_font_scale = 0.5
            (det_width, det_height), _ = cv2.getTextSize(det_text, font, det_font_scale, 1)

            det_x = 20
            det_y = 30

            # 检测数量背景
            overlay = frame.copy()
            cv2.rectangle(overlay, (det_x - 5, det_y - det_height - 5),
                         (det_x + det_width + 5, det_y + 5), (0, 0, 0), -1)
            cv2.addWeighted(frame, 0.8, overlay, 0.2, 0, frame)

            cv2.putText(frame, det_text, (det_x, det_y), font, det_font_scale, (100, 255, 100), 1)


def main():
    parser = argparse.ArgumentParser(description='ONNX实时摄像头推理 - 科技感UI版本')
    parser.add_argument('--model', type=str, default='converted_models/best.onnx', help='ONNX模型路径')
    parser.add_argument('--camera', type=int, default=0, help='摄像头设备ID')
    parser.add_argument('--conf-threshold', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--nms-threshold', type=float, default=0.45, help='NMS阈值')
    parser.add_argument('--target-size', type=int, default=640, help='输入图像尺寸')
    parser.add_argument('--width', type=int, default=1280, help='摄像头宽度')
    parser.add_argument('--height', type=int, default=720, help='摄像头高度')
    parser.add_argument('--fps', type=int, default=30, help='摄像头FPS')
    parser.add_argument('--save-video', type=str, help='保存视频路径')
    parser.add_argument('--no-display', action='store_true', help='不显示画面（仅推理）')

    args = parser.parse_args()

    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ ONNX模型文件不存在: {args.model}")
        return

    # 初始化推理器
    try:
        print("🚀 初始化ONNX推理器...")
        inference_engine = RealTimeONNXInference(
            model_path=args.model,
            conf_threshold=args.conf_threshold,
            nms_threshold=args.nms_threshold,
            target_size=args.target_size
        )
    except Exception as e:
        print(f"❌ 推理器初始化失败: {e}")
        return

    # 初始化摄像头
    print(f"📷 初始化摄像头 (设备ID: {args.camera})...")
    cap = cv2.VideoCapture(args.camera)

    if not cap.isOpened():
        print(f"❌ 无法打开摄像头设备: {args.camera}")
        return

    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)
    cap.set(cv2.CAP_PROP_FPS, args.fps)

    # 获取实际摄像头参数
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    actual_fps = cap.get(cv2.CAP_PROP_FPS)

    print(f"📷 摄像头参数: {actual_width}x{actual_height} @ {actual_fps:.1f}FPS")

    # 初始化视频保存
    video_writer = None
    if args.save_video:
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(args.save_video, fourcc, actual_fps,
                                     (actual_width, actual_height))
        print(f"📹 视频将保存到: {args.save_video}")

    # 显示设置
    display_enabled = not args.no_display
    if display_enabled:
        cv2.namedWindow('ONNX Real-time Inference - Tech UI', cv2.WINDOW_AUTOSIZE)
        print("🖥️  科技感实时显示已启用")
        print("💡 按 'q' 退出，按 's' 截图，按 'r' 重置统计")

    print("🎯 开始实时推理...")
    print("="*60)

    frame_count = 0
    start_time = time.time()
    last_print_time = time.time()

    try:
        while True:
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break

            frame_count += 1

            # 推理
            detections, inference_time = inference_engine.inference(frame)

            # 绘制科技感检测结果
            annotated_frame = inference_engine.draw_detections(frame, detections, inference_time)

            # 保存视频
            if video_writer:
                video_writer.write(annotated_frame)

            # 显示画面
            if display_enabled:
                cv2.imshow('ONNX Real-time Inference - Tech UI', annotated_frame)

                # 处理按键
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 用户退出")
                    break
                elif key == ord('s'):
                    # 截图
                    screenshot_path = f"screenshot_{int(time.time())}.jpg"
                    cv2.imwrite(screenshot_path, annotated_frame)
                    print(f"📸 截图保存到: {screenshot_path}")
                elif key == ord('r'):
                    # 重置统计
                    inference_engine.fps_queue.clear()
                    inference_engine.inference_times.clear()
                    frame_count = 0
                    start_time = time.time()
                    last_print_time = time.time()
                    print("🔄 统计数据已重置")

            # 打印检测结果（每5秒打印一次）
            current_time = time.time()
            if current_time - last_print_time >= 5.0:
                elapsed_time = current_time - start_time
                avg_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                avg_inference = np.mean(inference_engine.inference_times) if inference_engine.inference_times else 0

                print(f"📊 帧数: {frame_count}, 平均FPS: {avg_fps:.1f}, "
                      f"平均推理时间: {avg_inference:.1f}ms, 当前检测: {len(detections)}")

                if detections:
                    print("🎯 检测到的物体:")
                    for det in detections:
                        print(f"   - {det['class_name']}: {det['confidence']:.3f}")

                last_print_time = current_time

    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在退出...")

    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理资源
        print("🧹 清理资源...")

        if cap:
            cap.release()
            print("✅ 摄像头已释放")

        if video_writer:
            video_writer.release()
            print("✅ 视频文件已保存")

        if display_enabled:
            cv2.destroyAllWindows()
            print("✅ 显示窗口已关闭")

        # 最终统计信息
        if frame_count > 0:
            total_time = time.time() - start_time
            avg_fps = frame_count / total_time
            avg_inference = np.mean(inference_engine.inference_times) if inference_engine.inference_times else 0
            print(f"📊 总计处理 {frame_count} 帧")
            print(f"📊 平均FPS: {avg_fps:.1f}")
            print(f"📊 平均推理时间: {avg_inference:.1f}ms")


if __name__ == '__main__':
    main()
