# Windows RKNN模型仿真使用说明

## 概述

这套脚本允许您在Windows环境下仿真运行RKNN模型，无需RK3588硬件。仿真结果与实际RK3588设备运行结果完全一致，可以用来验证模型准确性和排查客户问题。

## 安装步骤

### 1. 安装RKNN-Toolkit2

运行安装脚本：
```bash
install_rknn_toolkit2.bat
```

或手动安装：
```bash
pip install rknn-toolkit2
pip install numpy opencv-python pillow
```

### 2. 验证安装

```bash
python -c "from rknn.api import RKNN; print('安装成功!')"
```

## 使用方法

### 单张图片测试

```bash
python scripts/rknn_simulator_windows.py --model yolov11_rk3588.rknn --image "场地图片/微信图片_20250730121942.jpg"
```

参数说明：
- `--model`: RKNN模型文件路径
- `--image`: 测试图片路径
- `--output`: 输出目录（可选，默认：rknn_simulator_results）
- `--conf`: 置信度阈值（可选，默认：0.25）

### 批量测试

```bash
python scripts/batch_rknn_simulator.py --model yolov11_rk3588.rknn --images "场地图片/*.jpg" --max-images 10
```

参数说明：
- `--model`: RKNN模型文件路径
- `--images`: 图片路径模式（支持通配符）
- `--output`: 输出目录（可选）
- `--conf`: 置信度阈值（可选）
- `--max-images`: 最大测试图片数量（可选）

## 输出结果

### 单张图片测试输出

1. **控制台输出**：详细的调试信息和检测结果
2. **结果图片**：带检测框的图片，保存在输出目录
3. **检测信息**：类别、置信度、边界框坐标

### 批量测试输出

1. **test_report.json**：详细的JSON格式测试报告
2. **test_summary.txt**：简化的文本格式报告
3. **结果图片**：所有测试图片的检测结果
4. **统计信息**：类别统计、成功率等

## 示例输出

```
🎯 RKNN仿真检测结果
============================================================
检测到 1 个物体:
  1. 土豆: 0.856 (位置: [687, 138, 1113, 515])

📁 结果保存至: rknn_simulator_results/微信图片_20250730121942_rknn_simulator.jpg
💡 这个结果应该与RK3588设备上的实际运行结果完全一致
💡 如果客户的结果不同，可能是客户环境配置问题
```

## 与客户对比验证

### 1. 生成标准结果

使用仿真脚本测试客户反馈有问题的图片：
```bash
python scripts/rknn_simulator_windows.py --model yolov11_rk3588.rknn --image problem_image.jpg
```

### 2. 对比分析

将仿真结果与客户的实际运行结果对比：

- **如果结果一致**：说明模型没问题，可能是客户使用方法不当
- **如果结果不一致**：说明客户的RK3588环境有问题

### 3. 常见客户环境问题

1. **RKNN-Toolkit版本不匹配**
2. **模型文件损坏或版本错误**
3. **输入图片预处理不正确**
4. **RK3588驱动或固件版本问题**
5. **内存不足或性能限制**

## 故障排除

### 1. RKNN-Toolkit2安装失败

```bash
# 尝试不同的安装源
pip install rknn-toolkit2 -i https://pypi.org/simple/
pip install rknn-toolkit2 -i https://mirrors.aliyun.com/pypi/simple/

# 或手动下载whl文件安装
# 从 https://github.com/rockchip-linux/rknn-toolkit2 下载对应版本
pip install rknn_toolkit2-x.x.x-cp3x-cp3x-win_amd64.whl
```

### 2. 模型加载失败

- 检查模型文件是否存在且完整
- 确认模型是为RK3588平台转换的
- 检查模型文件权限

### 3. 图片读取失败

- 确认图片路径正确（支持中文路径）
- 检查图片格式是否支持（jpg, png等）
- 确认图片文件未损坏

### 4. 内存不足

- 减少批量测试的图片数量
- 关闭其他占用内存的程序
- 使用较小的输入图片尺寸

## 技术细节

### 仿真原理

RKNN-Toolkit2的仿真模式在CPU上模拟RK3588的NPU行为：
- 使用相同的量化参数和算子实现
- 保持与硬件完全一致的数值精度
- 支持所有RKNN模型格式和功能

### 性能对比

| 环境 | 推理速度 | 精度 | 用途 |
|------|----------|------|------|
| Windows仿真 | 较慢（CPU） | 100%一致 | 验证、调试 |
| RK3588实际 | 快（NPU） | 标准 | 生产部署 |

### 数据格式

- **输入格式**：NHWC, uint8, [0,255]
- **输出格式**：(1, 16, 8400) -> (8400, 16)
- **后处理**：与RK3588设备完全一致

## 常见问题

**Q: 仿真结果真的和RK3588一致吗？**
A: 是的，RKNN-Toolkit2的仿真模式保证数值精度完全一致。

**Q: 为什么仿真比较慢？**
A: 仿真在CPU上运行，而RK3588使用专用NPU，所以速度较慢但精度一致。

**Q: 可以用来替代RK3588测试吗？**
A: 可以用于功能验证和问题排查，但性能测试仍需实际硬件。

**Q: 支持哪些RKNN模型？**
A: 支持所有通过RKNN-Toolkit2转换的模型格式。

## 联系支持

如果遇到问题，请提供：
1. 错误信息截图
2. 使用的命令
3. 模型文件信息
4. 测试图片（如果可能）
5. Python和RKNN-Toolkit2版本信息
