#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX模型推理测试脚本
用于在本地验证类别映射和后处理逻辑是否正确
与RKNN推理脚本使用相同的预处理和后处理逻辑
"""

import os
import cv2
import numpy as np
import time
import argparse
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import onnxruntime as ort

# 类别映射（包含所有类别，board类别会在后处理中过滤）
CLASS_NAMES = {
    0: 'apple',
    1: 'banana',
    2: 'board',      # 会被过滤掉
    3: 'cake',
    4: 'chili',
    5: 'cola',
    6: 'greenlight',
    7: 'milk',
    8: 'potato',
    9: 'redlight',
    10: 'tomato',
    11: 'watermelon'
}

# 中文类别名称
CLASS_NAMES_CN = {
    0: '苹果',
    1: '香蕉',
    2: '板子',      # 会被过滤掉
    3: '蛋糕', 
    4: '彩椒',
    5: '可乐',
    6: '绿灯',
    7: '牛奶',
    8: '土豆',
    9: '红灯',
    10: '西红柿',
    11: '西瓜'
}

# 类别颜色
CLASS_COLORS = {
    0: (255, 0, 0),     # 苹果 - 红色
    1: (255, 255, 0),   # 香蕉 - 黄色
    2: (128, 128, 128), # 板子 - 灰色（会被过滤）
    3: (255, 192, 203), # 蛋糕 - 粉色
    4: (255, 69, 0),    # 彩椒 - 橙红色
    5: (139, 69, 19),   # 可乐 - 棕色
    6: (0, 255, 0),     # 绿灯 - 绿色
    7: (255, 255, 255), # 牛奶 - 白色
    8: (160, 82, 45),   # 土豆 - 棕色
    9: (255, 0, 0),     # 红灯 - 红色
    10: (255, 99, 71),  # 西红柿 - 番茄红
    11: (0, 128, 0)     # 西瓜 - 深绿色
}

def preprocess_image(image_path, img_size=640):
    """图像预处理 - 与RKNN版本相同"""
    # 加载图像 - 支持中文路径
    try:
        # 使用numpy读取支持中文路径
        img_array = np.fromfile(image_path, dtype=np.uint8)
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        if img is None:
            return None, None, None
    except Exception as e:
        print(f"❌ 读取图像失败: {e}")
        return None, None, None
    
    original_img = img.copy()
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # 获取原始尺寸
    h, w = img_rgb.shape[:2]
    
    # 计算缩放比例
    scale = min(img_size / h, img_size / w)
    new_h, new_w = int(h * scale), int(w * scale)
    
    # 调整图像大小
    img_resized = cv2.resize(img_rgb, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    
    # 创建填充图像
    padded_img = np.full((img_size, img_size, 3), 114, dtype=np.uint8)
    
    # 计算填充位置
    top = (img_size - new_h) // 2
    left = (img_size - new_w) // 2
    
    # 放置图像
    padded_img[top:top+new_h, left:left+new_w] = img_resized
    
    # ONNX需要NCHW格式，float32数据类型，归一化到[0,1]
    padded_img = padded_img.astype(np.float32) / 255.0
    padded_img = np.transpose(padded_img, (2, 0, 1))  # HWC -> CHW
    padded_img = np.expand_dims(padded_img, axis=0)   # 添加批次维度
    
    return padded_img, original_img, scale

def postprocess_output(output, img_shape, scale, conf_threshold=0.25, iou_threshold=0.45):
    """后处理ONNX输出 - 与RKNN版本逻辑相同"""
    print(f"🔍 调试信息 - 原始输出形状: {output.shape}")
    print(f"🔍 调试信息 - 输出数据类型: {output.dtype}")
    print(f"🔍 调试信息 - 输出范围: [{output.min():.3f}, {output.max():.3f}]")
    
    # ONNX输出通常是 (1, 16, 8400) 格式
    if len(output.shape) == 3:
        output = output[0]  # 移除批次维度
        print(f"🔍 调试信息 - 移除批次维度后形状: {output.shape}")
    
    if output.shape[0] == 16:
        output = output.T   # 转置: (8400, 16)
        print(f"🔍 调试信息 - 转置后形状: {output.shape}")
    
    # 提取边界框和类别概率
    boxes = output[:, :4]  # (8400, 4)
    scores = output[:, 4:] # (8400, 12)
    
    print(f"🔍 调试信息 - 边界框形状: {boxes.shape}")
    print(f"🔍 调试信息 - 分数形状: {scores.shape}")
    print(f"🔍 调试信息 - 边界框范围: [{boxes.min():.3f}, {boxes.max():.3f}]")
    print(f"🔍 调试信息 - 分数范围: [{scores.min():.3f}, {scores.max():.3f}]")
    
    # 获取最大类别概率和索引
    max_scores = np.max(scores, axis=1)  # (8400,)
    class_ids = np.argmax(scores, axis=1)  # (8400,)
    
    print(f"🔍 调试信息 - 最高置信度: {max_scores.max():.3f}")
    print(f"🔍 调试信息 - 超过阈值的检测数: {np.sum(max_scores >= conf_threshold)}")
    
    # 过滤低置信度检测
    valid_indices = max_scores >= conf_threshold
    
    if not np.any(valid_indices):
        return []
    
    boxes = boxes[valid_indices]
    max_scores = max_scores[valid_indices]
    class_ids = class_ids[valid_indices]
    
    # 转换边界框格式 (center_x, center_y, width, height) -> (x1, y1, x2, y2)
    x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
    x1 = x_center - width / 2
    y1 = y_center - height / 2
    x2 = x_center + width / 2
    y2 = y_center + height / 2
    
    boxes = np.column_stack([x1, y1, x2, y2])
    
    # 应用NMS
    indices = cv2.dnn.NMSBoxes(
        boxes.tolist(), 
        max_scores.tolist(), 
        conf_threshold, 
        iou_threshold
    )
    
    detections = []
    if len(indices) > 0:
        indices = indices.flatten()
        
        for i in indices:
            x1, y1, x2, y2 = boxes[i]
            conf = max_scores[i]
            class_id = class_ids[i]
            
            # 过滤board类别 (class_id = 2)
            if class_id == 2:
                print(f"🔍 调试信息 - 过滤board类别检测: 置信度={conf:.3f}")
                continue
            
            # 只保留我们关心的类别
            if class_id not in CLASS_NAMES:
                print(f"🔍 调试信息 - 未知类别ID: {class_id}, 置信度={conf:.3f}")
                continue
            
            print(f"🔍 调试信息 - 检测到: {CLASS_NAMES[class_id]}({CLASS_NAMES_CN[class_id]}) 置信度={conf:.3f} 原始坐标=({x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f})")
            
            # 缩放回原始图像尺寸
            orig_h, orig_w = img_shape[:2]
            x1 = (x1 - (640 - orig_w * scale) / 2) / scale
            y1 = (y1 - (640 - orig_h * scale) / 2) / scale
            x2 = (x2 - (640 - orig_w * scale) / 2) / scale
            y2 = (y2 - (640 - orig_h * scale) / 2) / scale
            
            # 确保边界框在图像范围内
            x1 = max(0, min(x1, orig_w))
            y1 = max(0, min(y1, orig_h))
            x2 = max(0, min(x2, orig_w))
            y2 = max(0, min(y2, orig_h))
            
            print(f"🔍 调试信息 - 缩放后坐标: ({x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f})")
            
            detections.append({
                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                'confidence': float(conf),
                'class_id': int(class_id),
                'class_name': CLASS_NAMES[class_id],
                'class_name_cn': CLASS_NAMES_CN[class_id]
            })
    
    return detections

def draw_detections(image, detections):
    """在图像上绘制检测结果"""
    img_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(img_pil)
    
    # 尝试加载字体
    try:
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            "C:/Windows/Fonts/arial.ttf",   # Arial
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]
        font = None
        for fp in font_paths:
            if os.path.exists(fp):
                font = ImageFont.truetype(fp, 20)
                break
        if font is None:
            font = ImageFont.load_default()
    except:
        font = ImageFont.load_default()
    
    for det in detections:
        x1, y1, x2, y2 = det['bbox']
        conf = det['confidence']
        class_id = det['class_id']
        class_name_cn = det['class_name_cn']
        
        # 获取颜色
        color = CLASS_COLORS.get(class_id, (0, 255, 0))
        
        # 绘制边界框
        draw.rectangle([x1, y1, x2, y2], outline=color, width=3)
        
        # 准备标签文本
        label = f"{class_name_cn} {conf:.2f}"
        
        # 计算文本大小
        bbox = draw.textbbox((0, 0), label, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 绘制文本背景
        draw.rectangle([x1, y1-text_height-5, x1+text_width+10, y1], fill=color)
        
        # 绘制文本
        draw.text((x1+5, y1-text_height-2), label, fill=(255, 255, 255), font=font)
    
    # 转换回OpenCV格式
    result_img = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
    return result_img

def main():
    parser = argparse.ArgumentParser(description='ONNX模型推理测试')
    parser.add_argument('--model', type=str, default='converted_models/best.onnx',
                       help='ONNX模型路径')
    parser.add_argument('--image', type=str, required=True,
                       help='输入图像路径')
    parser.add_argument('--output', type=str, default='onnx_test_results',
                       help='输出目录')
    parser.add_argument('--conf', type=float, default=0.25,
                       help='置信度阈值')
    
    args = parser.parse_args()
    
    # 检查模型文件
    if not os.path.exists(args.model):
        print(f"❌ ONNX模型文件不存在: {args.model}")
        return
    
    # 检查图像文件
    if not os.path.exists(args.image):
        print(f"❌ 图像文件不存在: {args.image}")
        return
    
    # 创建输出目录
    os.makedirs(args.output, exist_ok=True)
    
    # 加载ONNX模型
    print(f"📦 加载ONNX模型: {args.model}")
    session = ort.InferenceSession(args.model)
    
    # 获取输入输出信息
    input_name = session.get_inputs()[0].name
    output_name = session.get_outputs()[0].name
    print(f"🔍 模型输入名称: {input_name}")
    print(f"🔍 模型输出名称: {output_name}")
    
    # 预处理图像
    print(f"📸 处理图像: {args.image}")
    processed_img, original_img, scale = preprocess_image(args.image)
    if processed_img is None:
        print(f"❌ 加载图像失败: {args.image}")
        return
    
    print(f"🔍 预处理后图像形状: {processed_img.shape}")
    print(f"🔍 缩放比例: {scale:.3f}")
    
    # 推理
    start_time = time.time()
    outputs = session.run([output_name], {input_name: processed_img})
    inference_time = time.time() - start_time
    
    print(f"⚡ 推理时间: {inference_time*1000:.2f} ms")
    
    # 后处理
    detections = postprocess_output(outputs[0], original_img.shape, scale, args.conf)
    
    # 绘制结果
    result_img = draw_detections(original_img, detections)
    
    # 保存结果
    output_path = os.path.join(args.output, f"{Path(args.image).stem}_onnx_test.jpg")
    cv2.imwrite(output_path, result_img)
    
    # 打印结果
    print(f"\n=== 检测结果 ===")
    print(f"检测到 {len(detections)} 个物体:")
    for det in detections:
        print(f"  {det['class_name_cn']}: {det['confidence']:.3f}")
    print(f"结果保存至: {output_path}")

if __name__ == '__main__':
    main()
