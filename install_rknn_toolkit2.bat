@echo off
echo ========================================
echo Windows RKNN-Toolkit2 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version
if %errorlevel% neq 0 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 正在检查pip...
pip --version
if %errorlevel% neq 0 (
    echo 错误: 未找到pip
    pause
    exit /b 1
)

echo.
echo ========================================
echo 开始安装RKNN-Toolkit2依赖包...
echo ========================================

echo 安装基础依赖...
pip install numpy opencv-python pillow

echo.
echo 安装RKNN-Toolkit2...
echo 注意: 如果下面的安装失败，请手动下载whl文件

echo 尝试从PyPI安装...
pip install rknn-toolkit2

if %errorlevel% neq 0 (
    echo.
    echo PyPI安装失败，请手动安装:
    echo 1. 访问: https://github.com/rockchip-linux/rknn-toolkit2
    echo 2. 下载对应的whl文件
    echo 3. 运行: pip install rknn_toolkit2-x.x.x-cp3x-cp3x-win_amd64.whl
    echo.
    echo 或者尝试以下命令:
    echo pip install rknn-toolkit2 -i https://pypi.org/simple/
    pause
    exit /b 1
)

echo.
echo ========================================
echo 验证安装...
echo ========================================

python -c "from rknn.api import RKNN; print('RKNN-Toolkit2 安装成功!')"
if %errorlevel% neq 0 (
    echo 验证失败，请检查安装
    pause
    exit /b 1
)

echo.
echo ========================================
echo 安装完成!
echo ========================================
echo 现在可以运行RKNN仿真脚本了:
echo python scripts/rknn_simulator_windows.py --model your_model.rknn --image your_image.jpg
echo.
pause
