#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO模型转换和量化主控制脚本
完整的PyTorch -> ONNX -> RKNN转换流程
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

def run_command(cmd, description):
    """运行命令并处理结果"""
    print(f"\n {description}")
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 执行成功")
        if result.stdout:
            print("输出:", result.stdout[-500:])  # 显示最后500字符
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 执行失败: {e}")
        if e.stdout:
            print("标准输出:", e.stdout[-500:])
        if e.stderr:
            print("错误输出:", e.stderr[-500:])
        return False

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")

    # 检查包的映射关系
    package_mapping = {
        'torch': 'torch',
        'ultralytics': 'ultralytics',
        'onnx': 'onnx',
        'onnxruntime': 'onnxruntime',
        'opencv-python': 'cv2',
        'numpy': 'numpy'
    }

    missing_packages = []

    for package_name, import_name in package_mapping.items():
        try:
            __import__(import_name)
            print(f"✅ {package_name}")
        except ImportError:
            print(f"❌ {package_name}")
            missing_packages.append(package_name)

    if missing_packages:
        print(f"缺少依赖: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False

    return True

def main():
    parser = argparse.ArgumentParser(description='Complete YOLO model conversion and quantization pipeline')
    parser.add_argument('--model', type=str, 
                       default='runs/train/yolo11n_11classes_20250731_144345/weights/best.pt',
                       help='Path to YOLO model (.pt)')
    parser.add_argument('--output-dir', type=str, default='converted_models',
                       help='Output directory for converted models')
    parser.add_argument('--img-size', type=int, default=640,
                       help='Input image size')
    parser.add_argument('--platform', type=str, default='rk3588',
                       choices=['rk3588', 'rk3566', 'rk3568', 'rk3562'],
                       help='Target RKNN platform')
    parser.add_argument('--quantization-samples', type=int, default=100,
                       help='Number of samples for quantization')
    parser.add_argument('--validation-samples', type=int, default=50,
                       help='Number of samples for validation')
    parser.add_argument('--skip-validation', action='store_true',
                       help='Skip model validation')
    parser.add_argument('--data-yaml', type=str, default='data/data.yaml',
                       help='Path to dataset YAML file')
    
    args = parser.parse_args()
    
    print("YOLO模型转换和量化流程")
    print("=" * 50)
    print(f"输入模型: {args.model}")
    print(f"输出目录: {args.output_dir}")
    print(f"图像尺寸: {args.img_size}")
    print(f"目标平台: {args.platform}")
    print(f"量化样本数: {args.quantization_samples}")
    print("=" * 50)
    
    # 检查输入模型
    if not os.path.exists(args.model):
        print(f"❌ 模型文件不存在: {args.model}")
        return
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 定义文件路径
    model_name = Path(args.model).stem
    onnx_path = output_dir / f"{model_name}.onnx"
    rknn_path = output_dir / f"{model_name}_{args.platform}.rknn"
    quantization_data_path = output_dir / "calibration_dataset.npy"
    
    # 步骤1: 转换为ONNX
    print("\n" + "="*50)
    print("步骤 1/4: PyTorch -> ONNX 转换")
    print("="*50)
    
    onnx_cmd = [
        sys.executable, 'scripts/convert_to_onnx.py',
        '--model', args.model,
        '--output', str(onnx_path),
        '--img-size', str(args.img_size),
        '--device', 'cpu'
    ]
    
    if not run_command(onnx_cmd, "转换为ONNX格式"):
        print("💥 ONNX转换失败，停止流程")
        return
    
    # 步骤2: 准备量化数据集
    print("\n" + "="*50)
    print("步骤 2/4: 准备量化数据集")
    print("="*50)
    
    quantization_cmd = [
        sys.executable, 'scripts/prepare_quantization_data.py',
        '--data-yaml', args.data_yaml,
        '--output', str(quantization_data_path),
        '--num-samples', str(args.quantization_samples),
        '--img-size', str(args.img_size)
    ]
    
    if not run_command(quantization_cmd, "准备量化数据集"):
        print("量化数据集准备失败，将使用随机数据")
        quantization_data_path = None
    
    # 步骤3: 转换为RKNN并量化
    print("\n" + "="*50)
    print("步骤 3/4: ONNX -> RKNN 转换和量化")
    print("="*50)
    
    rknn_cmd = [
        sys.executable, 'scripts/convert_to_rknn.py',
        '--onnx', str(onnx_path),
        '--output', str(rknn_path),
        '--platform', args.platform,
        '--img-size', str(args.img_size)
    ]
    
    if quantization_data_path and os.path.exists(quantization_data_path):
        rknn_cmd.extend(['--dataset', str(quantization_data_path)])
    
    if not run_command(rknn_cmd, "转换为RKNN格式并量化"):
        print("RKNN转换失败，停止流程")
        return
    
    # 步骤4: 验证模型精度
    if not args.skip_validation:
        print("\n" + "="*50)
        print("步骤 4/4: 模型精度验证")
        print("="*50)
        
        validation_cmd = [
            sys.executable, 'scripts/validate_rknn_model.py',
            '--onnx', str(onnx_path),
            '--rknn', str(rknn_path),
            '--data-yaml', args.data_yaml,
            '--num-samples', str(args.validation_samples),
            '--img-size', str(args.img_size)
        ]
        
        if not run_command(validation_cmd, "验证模型精度"):
            print("模型验证失败，但转换已完成")
    
    # 完成总结
    print("\n" + "="*50)
    print("转换流程完成!")
    print("="*50)
    print(f"输出目录: {output_dir}")
    print(f"ONNX模型: {onnx_path}")
    print(f"RKNN模型: {rknn_path}")
    
    if os.path.exists(onnx_path):
        onnx_size = os.path.getsize(onnx_path) / (1024 * 1024)
        print(f"ONNX模型大小: {onnx_size:.2f} MB")
    
    if os.path.exists(rknn_path):
        rknn_size = os.path.getsize(rknn_path) / (1024 * 1024)
        print(f"RKNN模型大小: {rknn_size:.2f} MB")
        
        if os.path.exists(onnx_path):
            compression_ratio = onnx_size / rknn_size
            print(f"压缩比: {compression_ratio:.2f}x")
    
    print("\n💡 使用说明:")
    print(f"1. ONNX模型可用于通用推理: {onnx_path}")
    print(f"2. RKNN模型可部署到{args.platform}设备: {rknn_path}")
    print("3. 建议在目标设备上进一步测试模型性能")

if __name__ == '__main__':
    main()
