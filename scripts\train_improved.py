#!/usr/bin/env python3
"""
改进的YOLO训练脚本 - 解决漏检和低置信度问题
使用更大模型、更多轮数、优化的超参数
"""

import os
import sys
import torch
import yaml
from pathlib import Path
from ultralytics import YOLO
from datetime import datetime
import argparse

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

class ImprovedYOLOTrainer:
    def __init__(self, config_path="data/data.yaml", model_size="s"):
        """
        改进的YOLO训练器
        
        Args:
            config_path: 数据配置文件路径
            model_size: 模型大小 ('n', 's', 'm', 'l', 'x')
        """
        self.project_root = project_root
        self.config_path = self.project_root / config_path
        self.model_size = model_size
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 验证配置文件
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
        # 加载配置
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
            
        print(f"🚀 改进训练器初始化完成")
        print(f"使用设备: {self.device}")
        print(f"模型大小: YOLOv11{model_size}")
        print(f"数据配置: {self.config_path}")
        print(f"类别数量: {self.config['nc']}")
        
    def download_model(self):
        """下载预训练模型"""
        model_name = f"yolo11{self.model_size}.pt"
        model_path = self.project_root / "scripts" / "model" / model_name
        
        if not model_path.exists():
            print(f"📥 下载预训练模型: {model_name}")
            # 创建模型目录
            model_path.parent.mkdir(parents=True, exist_ok=True)
            # YOLO会自动下载模型
            model = YOLO(model_name)
            # 保存到指定位置
            model.save(str(model_path))
        else:
            print(f"✅ 模型已存在: {model_path}")
            
        return str(model_path)
        
    def train_improved_model(self, 
                           epochs=300,
                           batch_size=16,
                           img_size=640,
                           patience=100,
                           project_name=None,
                           resume=None):
        """
        使用改进参数训练模型
        
        Args:
            epochs: 训练轮数 (增加到300)
            batch_size: 批次大小
            img_size: 输入图像尺寸
            patience: 早停耐心值 (增加到100)
            project_name: 项目名称
            resume: 恢复训练的检查点路径
        """
        
        # 获取预训练模型
        if resume:
            model_path = resume
            print(f"🔄 从检查点恢复训练: {model_path}")
        else:
            model_path = self.download_model()
            print(f"🎯 使用预训练模型: {model_path}")
        
        # 创建模型
        model = YOLO(model_path)
        
        # 生成项目名称
        if project_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            project_name = f"yolo11{self.model_size}_improved_{timestamp}"
        
        print(f"🏋️ 开始改进训练...")
        print(f"📊 训练参数:")
        print(f"  - 轮数: {epochs}")
        print(f"  - 批次大小: {batch_size}")
        print(f"  - 图像尺寸: {img_size}")
        print(f"  - 早停耐心: {patience}")
        print(f"  - 项目名称: {project_name}")
        
        # 改进的训练参数
        train_args = {
            'data': str(self.config_path),
            'epochs': epochs,
            'batch': batch_size,
            'imgsz': img_size,
            'device': self.device,
            'project': 'runs/train',
            'name': project_name,
            'patience': patience,
            'save_period': 10,  # 每10轮保存一次
            'val': True,
            'plots': True,
            'verbose': True,
            
            # 优化的超参数
            'lr0': 0.01,        # 初始学习率
            'lrf': 0.01,        # 最终学习率因子
            'momentum': 0.937,   # SGD动量
            'weight_decay': 0.0005,  # 权重衰减
            'warmup_epochs': 3,  # 预热轮数
            'warmup_momentum': 0.8,  # 预热动量
            'warmup_bias_lr': 0.1,   # 预热偏置学习率
            
            # 数据增强 (增强版)
            'hsv_h': 0.015,     # 色调增强
            'hsv_s': 0.7,       # 饱和度增强
            'hsv_v': 0.4,       # 明度增强
            'degrees': 10.0,    # 旋转角度
            'translate': 0.1,   # 平移
            'scale': 0.5,       # 缩放
            'shear': 2.0,       # 剪切
            'perspective': 0.0001,  # 透视变换
            'flipud': 0.0,      # 上下翻转
            'fliplr': 0.5,      # 左右翻转
            'mosaic': 1.0,      # 马赛克增强
            'mixup': 0.1,       # 混合增强
            'copy_paste': 0.1,  # 复制粘贴增强
            
            # 损失函数权重
            'box': 7.5,         # 边界框损失权重
            'cls': 0.5,         # 分类损失权重
            'dfl': 1.5,         # 分布焦点损失权重
            
            # NMS参数
            'conf': 0.001,      # 置信度阈值 (训练时用更低值)
            'iou': 0.7,         # NMS IoU阈值
            
            # 其他优化
            'amp': True,        # 自动混合精度
            'fraction': 1.0,    # 使用全部数据
            'profile': False,   # 不进行性能分析
            'freeze': None,     # 不冻结层
            'multi_scale': True, # 多尺度训练
            'overlap_mask': True, # 重叠掩码
            'mask_ratio': 4,    # 掩码比例
            'dropout': 0.0,     # Dropout
            'val_period': 1,    # 验证周期
        }
        
        try:
            # 开始训练
            results = model.train(**train_args)
            
            # 获取最佳模型路径
            best_model_path = Path(f"runs/train/{project_name}/weights/best.pt")
            last_model_path = Path(f"runs/train/{project_name}/weights/last.pt")
            
            print(f"\n🎉 改进训练完成!")
            print(f"最佳权重: {best_model_path}")
            print(f"最后权重: {last_model_path}")
            
            # 验证最佳模型
            if best_model_path.exists():
                print(f"\n🔍 验证最佳模型...")
                best_model = YOLO(str(best_model_path))
                val_results = best_model.val(data=str(self.config_path))
                
                # 检查是否达到目标
                map50 = val_results.box.map50
                print(f"📊 最终 mAP50: {map50:.3f}")
                
                if map50 >= 0.90:
                    print(f"🎯 恭喜！达到90%目标准确率!")
                else:
                    print(f"⚠️ 当前准确率: {map50*100:.1f}%, 距离90%目标还差: {(0.90-map50)*100:.1f}%")
                    print(f"💡 建议: 继续训练或调整参数")
            
            return str(best_model_path), str(last_model_path)
            
        except Exception as e:
            print(f"❌ 训练过程中出错: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='改进的YOLO训练脚本')
    parser.add_argument('--model-size', type=str, default='s', 
                       choices=['n', 's', 'm', 'l', 'x'],
                       help='模型大小 (n=nano, s=small, m=medium, l=large, x=xlarge)')
    parser.add_argument('--epochs', type=int, default=300, help='训练轮数')
    parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    parser.add_argument('--img-size', type=int, default=640, help='输入图像尺寸')
    parser.add_argument('--patience', type=int, default=100, help='早停耐心值')
    parser.add_argument('--project-name', type=str, help='项目名称')
    parser.add_argument('--resume', type=str, help='从检查点恢复训练')
    parser.add_argument('--config', type=str, default='data/data.yaml', help='数据配置文件')
    
    args = parser.parse_args()
    
    try:
        # 创建改进训练器
        trainer = ImprovedYOLOTrainer(
            config_path=args.config,
            model_size=args.model_size
        )
        
        # 开始改进训练
        best_path, last_path = trainer.train_improved_model(
            epochs=args.epochs,
            batch_size=args.batch_size,
            img_size=args.img_size,
            patience=args.patience,
            project_name=args.project_name,
            resume=args.resume
        )
        
        print(f"\n✅ 训练完成!")
        print(f"🎯 建议使用以下命令测试改进效果:")
        print(f"python scripts/predict_optimized.py --model {best_path} --conf 0.3")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
