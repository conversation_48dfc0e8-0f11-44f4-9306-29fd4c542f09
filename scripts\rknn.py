import cv2
import numpy as np
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import time

# 类别名称映射（根据data.yaml的正确顺序）
CLASS_NAMES = {
    0: 'apple', 1: 'banana', 2: 'board', 3: 'cake', 4: 'chili', 5: 'cola',
    6: 'greenlight', 7: 'milk', 8: 'potato', 9: 'redlight', 10: 'tomato', 11: 'watermelon'
}

CLASS_NAMES_CN = {
    0: '苹果', 1: '香蕉', 2: '板子', 3: '蛋糕', 4: '彩椒', 5: '可乐',
    6: '绿灯', 7: '牛奶', 8: '土豆', 9: '红灯', 10: '西红柿', 11: '西瓜'
}

# 类别颜色映射（固定颜色）
CLASS_COLORS = {
    0: (255, 0, 0),     # 苹果 - 红色
    1: (255, 255, 0),   # 香蕉 - 黄色
    2: (128, 128, 128), # 板子 - 灰色（会被过滤）
    3: (255, 192, 203), # 蛋糕 - 粉色
    4: (255, 69, 0),    # 彩椒 - 橙红色
    5: (139, 69, 19),   # 可乐 - 棕色
    6: (0, 255, 0),     # 绿灯 - 绿色
    7: (255, 255, 255), # 牛奶 - 白色
    8: (160, 82, 45),   # 土豆 - 棕色
    9: (255, 0, 0),     # 红灯 - 红色
    10: (255, 99, 71),  # 西红柿 - 番茄红
    11: (0, 128, 0)     # 西瓜 - 深绿色
}


def check_rknn_toolkit():
    """检查RKNN-Toolkit是否安装"""
    try:
        from rknn.api import RKNN
        return True
    except ImportError:
        print("❌ RKNN-Toolkit未安装")
        print("请在RK3588设备上安装RKNN-Toolkit2:")
        print("pip install rknn_toolkit2")
        return False


def preprocess_image(image, img_size=640):
    """图像预处理"""
    img_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 获取原始尺寸
    h, w = img_rgb.shape[:2]
    
    # 计算缩放比例
    scale = min(img_size / h, img_size / w)
    new_h, new_w = int(h * scale), int(w * scale)
    
    # 调整图像大小
    img_resized = cv2.resize(img_rgb, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    
    # 创建填充图像
    padded_img = np.full((img_size, img_size, 3), 114, dtype=np.uint8)
    
    # 计算填充位置
    top = (img_size - new_h) // 2
    left = (img_size - new_w) // 2
    
    # 放置图像
    padded_img[top:top+new_h, left:left+new_w] = img_resized
    
    # RKNN需要NHWC格式，uint8数据类型
    padded_img = np.expand_dims(padded_img, axis=0)  # 添加批次维度
    
    return padded_img, scale, top, left


def postprocess_output(output, img_shape, scale, top, left, conf_threshold=0.25, iou_threshold=0.45):
    """后处理RKNN输出，只保留置信度最高的结果"""
    # 处理输出格式
    if len(output.shape) == 3:
        output = output[0]  # 移除批次维度
    
    if output.shape[0] == 16:
        output = output.T   # 转置: (8400, 16)
    
    # 提取边界框和类别概率
    boxes = output[:, :4]  # (8400, 4)
    scores = output[:, 4:] # (8400, 12)
    
    # 获取最大类别概率和索引
    max_scores = np.max(scores, axis=1)  # (8400,)
    class_ids = np.argmax(scores, axis=1)  # (8400,)
    
    # 过滤低置信度检测
    valid_indices = max_scores >= conf_threshold
    
    if not np.any(valid_indices):
        return []
    
    boxes = boxes[valid_indices]
    max_scores = max_scores[valid_indices]
    class_ids = class_ids[valid_indices]
    
    # 转换边界框格式 (center_x, center_y, width, height) -> (x1, y1, x2, y2)
    x_center, y_center, width, height = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
    x1 = x_center - width / 2
    y1 = y_center - height / 2
    x2 = x_center + width / 2
    y2 = y_center + height / 2
    
    boxes = np.column_stack([x1, y1, x2, y2])
    
    # 应用NMS
    indices = cv2.dnn.NMSBoxes(
        boxes.tolist(), 
        max_scores.tolist(), 
        conf_threshold, 
        iou_threshold
    )
    
    detections = []

    if len(indices) > 0:
        indices = indices.flatten()
        
        for i in indices:
            x1, y1, x2, y2 = boxes[i]
            conf = max_scores[i]
            class_id = class_ids[i]
            
            # 过滤board类别 (class_id = 2)
            if class_id == 2:
                continue

            # 过滤不关心的类别
            if class_id not in CLASS_NAMES:
                continue
            
            # 缩放回原始图像尺寸
            orig_h, orig_w = img_shape[:2]
            x1 = (x1 - left) / scale
            y1 = (y1 - top) / scale
            x2 = (x2 - left) / scale
            y2 = (y2 - top) / scale
            
            # 确保边界框在图像范围内
            x1 = max(0, min(x1, orig_w))
            y1 = max(0, min(y1, orig_h))
            x2 = max(0, min(x2, orig_w))
            y2 = max(0, min(y2, orig_h))
            
            detections.append({
                'bbox': [int(x1), int(y1), int(x2), int(y2)],
                'confidence': float(conf),
                'class_id': int(class_id),
                'class_name': CLASS_NAMES[class_id],
                'class_name_cn': CLASS_NAMES_CN[class_id]
            })
    
    # 只保留置信度最高的一个检测结果
    if detections:
        return [max(detections, key=lambda x: x['confidence'])]
    return []


def draw_detections(image, detections, fps=0):
    """在图像上绘制单一识别框"""
    img_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    draw = ImageDraw.Draw(img_pil)
    
    # 尝试加载字体（优先确保中文显示）
    try:
        font_paths = [
            "C:/Windows/Fonts/simhei.ttf",   # 中文字体
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",  # Linux中文
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/System/Library/Fonts/PingFang.ttc",  # macOS中文
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # 备选
            "C:/Windows/Fonts/arial.ttf"
        ]
        font = None
        for fp in font_paths:
            if Path(fp).exists():
                font = ImageFont.truetype(fp, 28)  # 增大字体便于观察
                break
        if font is None:
            font = ImageFont.load_default()
    except:
        font = ImageFont.load_default()
    
    # 绘制单一检测框
    for det in detections:
        x1, y1, x2, y2 = det['bbox']
        conf = det['confidence']
        class_id = det['class_id']
        class_name_cn = det['class_name_cn']
        
        # 获取颜色（使用更醒目的颜色）
        color = CLASS_COLORS.get(class_id, (0, 255, 0))
        
        # 绘制边界框（加粗边框，更醒目）
        draw.rectangle([x1, y1, x2, y2], outline=color, width=6)
        
        # 准备标签文本
        label = f"{class_name_cn} {conf:.2f}"
        
        # 计算文本大小
        bbox = draw.textbbox((0, 0), label, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 绘制文本背景（更大的背景框）
        draw.rectangle([x1, y1-text_height-10, x1+text_width+15, y1], fill=color)
        
        # 绘制文本
        draw.text((x1+8, y1-text_height-5), label, fill=(255, 255, 255), font=font)
    
    # 绘制FPS（增大字体）
    if fps > 0:
        fps_text = f"FPS: {fps:.1f}"
        draw.text((15, 15), fps_text, fill=(0, 255, 0), font=font)
    
    # 转换回OpenCV格式
    result_img = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
    return result_img


class RKNNVisualizer:
    """RKNN模型可视化测试工具"""
    
    def __init__(self, model_path, conf_threshold=0.25, img_size=640):
        self.model_path = model_path
        self.conf_threshold = conf_threshold
        self.img_size = img_size
        self.rknn = None
        self.initialized = False
        self.detections = []  # 存储最新检测结果
        
    def initialize(self):
        """初始化RKNN模型"""
        if not check_rknn_toolkit():
            return False
            
        from rknn.api import RKNN
        
        # 创建RKNN对象
        self.rknn = RKNN(verbose=False)
        
        try:
            # 加载RKNN模型
            ret = self.rknn.load_rknn(self.model_path)
            if ret != 0:
                print(f"❌ 加载RKNN模型失败: {self.model_path}")
                return False
            
            # 初始化运行时环境
            ret = self.rknn.init_runtime(target='rk3588')
            if ret != 0:
                print("❌ 初始化RK3588运行时失败")
                return False
            
            print("✅ RKNN模型初始化成功")
            self.initialized = True
            return True
            
        except Exception as e:
            print(f"❌ RKNN初始化错误: {e}")
            return False
    
    def process_frame(self, frame):
        """处理单帧图像"""
        if not self.initialized:
            print("❌ RKNN模型未初始化")
            return None, 0
        
        start_time = time.time()
        
        # 预处理图像
        processed_img, scale, top, left = preprocess_image(frame, self.img_size)
        
        try:
            # 推理
            outputs = self.rknn.inference(inputs=[processed_img], data_format=['nhwc'])
            inference_time = time.time() - start_time
            
            if outputs is None or len(outputs) == 0:
                print("❌ RKNN推理失败")
                self.detections = []
                return frame, 0
            
            # 后处理（只保留最高置信度结果）
            self.detections = postprocess_output(
                outputs[0], 
                frame.shape, 
                scale, 
                top, 
                left,
                self.conf_threshold
            )
            
            # 计算FPS
            fps = 1.0 / inference_time if inference_time > 0 else 0
            
            # 绘制结果
            result_img = draw_detections(frame, self.detections, fps)
            
            return result_img, fps
            
        except Exception as e:
            print(f"❌ 推理过程错误: {e}")
            self.detections = []
            return frame, 0
    
    def release(self):
        """释放资源"""
        if self.rknn:
            self.rknn.release()
            self.initialized = False


def main():
    # 设置输入源为/dev/video0
    source = "/dev/video0"
    window_title = "RKNN 实时检测 - 单一识别框"
    
    # 解析模型路径参数
    import argparse
    parser = argparse.ArgumentParser(description='RKNN模型可视化测试工具（单一识别框）')
    parser.add_argument('--model', type=str, required=True, help='RKNN模型路径')
    parser.add_argument('--conf', type=float, default=0.25, help='置信度阈值')
    parser.add_argument('--img-size', type=int, default=640, help='输入图像尺寸')
    parser.add_argument('--output', type=str, help='保存输出视频路径 (可选)')
    
    args = parser.parse_args()
    
    # 初始化可视化工具
    visualizer = RKNNVisualizer(
        model_path=args.model,
        conf_threshold=args.conf,
        img_size=args.img_size
    )
    
    if not visualizer.initialize():
        return
    
    try:
        # 打开指定摄像头（设置缓冲大小，减少延迟）
        cap = cv2.VideoCapture(source)
        cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        
        if not cap.isOpened():
            print(f"❌ 无法打开摄像头: {source}")
            return
        
        # 视频写入器（如果需要）
        out_writer = None
        if args.output:
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out_writer = cv2.VideoWriter(args.output, fourcc, fps, (width, height))
        
        print("📌 提示: 按 'q' 退出，按 's' 保存当前帧")
        
        # 创建窗口并设置为可调整大小
        cv2.namedWindow(window_title, cv2.WINDOW_NORMAL)
        cv2.resizeWindow(window_title, 1280, 720)  # 增大窗口尺寸
        
        # 主循环
        while True:
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法获取摄像头帧")
                break
            
            # 处理帧
            result_frame, fps = visualizer.process_frame(frame)
            
            # 打印单一检测结果
            print("\n" + "="*50)
            print(f"检测时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"FPS: {fps:.1f}")
            print("识别结果（最高置信度）:")
            if visualizer.detections:
                det = visualizer.detections[0]
                print(f"  类别: {det['class_name_cn']} | 置信度: {det['confidence']:.2f} | 位置: {det['bbox']}")
            else:
                print("  未检测到任何物体")
            print("="*50 + "\n")
            
            # 显示结果
            cv2.imshow(window_title, result_frame)
            
            # 保存视频（如果需要）
            if out_writer:
                out_writer.write(result_frame)
            
            # 按键处理
            key = cv2.waitKey(10) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                save_path = f"rknn_snapshot_{time.time():.0f}.jpg"
                cv2.imwrite(save_path, result_frame)
                print(f"📸 已保存快照: {save_path}")
        
        # 释放资源
        cap.release()
        if out_writer:
            out_writer.release()
        cv2.destroyAllWindows()
        
    finally:
        visualizer.release()


if __name__ == "__main__":
    main()