#!/bin/bash
# RKNN转换自动化脚本

echo "🚀 开始YOLO模型RKNN转换流程"

# 检查Python环境
python3 --version
if [ $? -ne 0 ]; then
    echo "❌ Python3未安装"
    exit 1
fi

# 安装依赖
echo "📦 安装依赖..."
pip install -r requirements.txt

# 检查RKNN-Toolkit
python3 -c "from rknn.api import RKNN" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ RKNN-Toolkit未安装，请手动安装:"
    echo "wget https://github.com/rockchip-linux/rknn-toolkit/releases/download/v1.6.0/rknn_toolkit-1.6.0-cp38-cp38-linux_x86_64.whl"
    echo "pip install rknn_toolkit-1.6.0-cp38-cp38-linux_x86_64.whl"
    exit 1
fi

# 步骤1: 准备量化数据集
echo "📊 准备量化数据集..."
python3 scripts/prepare_quantization_data.py \
    --data-yaml data/data.yaml \
    --output output/calibration_dataset.npy \
    --num-samples 100 \
    --img-size 640

# 步骤2: 转换为RKNN
echo "🔄 转换为RKNN格式..."
python3 scripts/convert_to_rknn_simple.py \
    --onnx models/best.onnx \
    --output output/best_rk3588.rknn \
    --dataset output/calibration_dataset.npy \
    --platform rk3588 \
    --img-size 640

# 检查结果
if [ -f "output/best_rk3588.rknn" ]; then
    echo "✅ RKNN转换成功!"
    echo "📁 输出文件: output/best_rk3588.rknn"
    echo "📊 文件大小: $(du -h output/best_rk3588.rknn | cut -f1)"
else
    echo "❌ RKNN转换失败!"
    exit 1
fi

echo "🎉 转换完成!"
