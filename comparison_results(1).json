{"timestamp": "20250801_152119", "config": {"onnx_model": "/home/<USER>/tool/best.onnx", "rknn_model": "/home/<USER>/tool/yolov11_rk3588_fp16.rknn", "conf_threshold": 0.25, "total_images": 5}, "results": [{"image_name": "apple.jpg", "onnx_detections": [{"class_id": 0, "class_name": "apple", "confidence": 0.9038754105567932, "bbox": [584.8626708984375, 70.24191284179688, 961.6950988769531, 434.9227066040039]}], "rknn_detections": [{"class_id": 7, "class_name": "milk", "confidence": 0.326416015625, "bbox": [560.8125, 65.25, 977.0625, 449.4375]}], "onnx_message": "ONNX inference successful, time: 404.57ms", "rknn_message": "RKNN inference successful, time: 206.38ms", "comparison": {"onnx_count": 1, "rknn_count": 1, "matches": [], "onnx_only": [{"class_id": 0, "class_name": "apple", "confidence": 0.9038754105567932, "bbox": [584.8626708984375, 70.24191284179688, 961.6950988769531, 434.9227066040039]}], "rknn_only": [{"class_id": 7, "class_name": "milk", "confidence": 0.326416015625, "bbox": [560.8125, 65.25, 977.0625, 449.4375]}], "differences": []}}, {"image_name": "cola.jpg", "onnx_detections": [{"class_id": 5, "class_name": "cola", "confidence": 0.762994647026062, "bbox": [862.9118981361389, 0.0, 1216.6424832344055, 612.5884949684144]}], "rknn_detections": [{"class_id": 5, "class_name": "cola", "confidence": 0.3779296875, "bbox": [876.990625, 4.1650390625, 1195.5328125, 606.2630859375]}], "onnx_message": "ONNX inference successful, time: 354.80ms", "rknn_message": "RKNN inference successful, time: 208.23ms", "comparison": {"onnx_count": 1, "rknn_count": 1, "matches": [], "onnx_only": [{"class_id": 5, "class_name": "cola", "confidence": 0.762994647026062, "bbox": [862.9118981361389, 0.0, 1216.6424832344055, 612.5884949684144]}], "rknn_only": [{"class_id": 5, "class_name": "cola", "confidence": 0.3779296875, "bbox": [876.990625, 4.1650390625, 1195.5328125, 606.2630859375]}], "differences": []}}, {"image_name": "greenlight.jpg", "onnx_detections": [{"class_id": 6, "class_name": "greenlight", "confidence": 0.5831001996994019, "bbox": [745.5242582321167, 3.4100443840026857, 1210.6966459274292, 508.4970101356506]}], "rknn_detections": [{"class_id": 6, "class_name": "greenlight", "confidence": 0.68798828125, "bbox": [746.375, 0.0, 1212.859375, 496.47265625]}, {"class_id": 6, "class_name": "greenlight", "confidence": 0.435302734375, "bbox": [913.64296875, 329.2046875, 1046.92421875, 485.14375]}], "onnx_message": "ONNX inference successful, time: 392.65ms", "rknn_message": "RKNN inference successful, time: 239.56ms", "comparison": {"onnx_count": 1, "rknn_count": 2, "matches": [], "onnx_only": [{"class_id": 6, "class_name": "greenlight", "confidence": 0.5831001996994019, "bbox": [745.5242582321167, 3.4100443840026857, 1210.6966459274292, 508.4970101356506]}], "rknn_only": [{"class_id": 6, "class_name": "greenlight", "confidence": 0.68798828125, "bbox": [746.375, 0.0, 1212.859375, 496.47265625]}, {"class_id": 6, "class_name": "greenlight", "confidence": 0.435302734375, "bbox": [913.64296875, 329.2046875, 1046.92421875, 485.14375]}], "differences": []}}, {"image_name": "cola2.jpg", "onnx_detections": [{"class_id": 5, "class_name": "cola", "confidence": 0.7165308594703674, "bbox": [828.060852432251, 15.465656399726868, 1199.7276250839234, 654.6528676986694]}], "rknn_detections": [{"class_id": 5, "class_name": "cola", "confidence": 0.25830078125, "bbox": [828.009765625, 19.9921875, 1195.199609375, 640.41640625]}], "onnx_message": "ONNX inference successful, time: 393.98ms", "rknn_message": "RKNN inference successful, time: 201.61ms", "comparison": {"onnx_count": 1, "rknn_count": 1, "matches": [], "onnx_only": [{"class_id": 5, "class_name": "cola", "confidence": 0.7165308594703674, "bbox": [828.060852432251, 15.465656399726868, 1199.7276250839234, 654.6528676986694]}], "rknn_only": [{"class_id": 5, "class_name": "cola", "confidence": 0.25830078125, "bbox": [828.009765625, 19.9921875, 1195.199609375, 640.41640625]}], "differences": []}}, {"image_name": "greenlight2.jpg", "onnx_detections": [{"class_id": 6, "class_name": "greenlight", "confidence": 0.7872973680496216, "bbox": [432.9989252090454, 2.209260368347168, 946.4197111129761, 527.1660655975342]}], "rknn_detections": [{"class_id": 6, "class_name": "greenlight", "confidence": 0.8017578125, "bbox": [433.9970703125, 0.0, 953.4607421875, 521.2962890625]}], "onnx_message": "ONNX inference successful, time: 390.79ms", "rknn_message": "RKNN inference successful, time: 267.44ms", "comparison": {"onnx_count": 1, "rknn_count": 1, "matches": [{"onnx": {"class_id": 6, "class_name": "greenlight", "confidence": 0.7872973680496216, "bbox": [432.9989252090454, 2.209260368347168, 946.4197111129761, 527.1660655975342]}, "rknn": {"class_id": 6, "class_name": "greenlight", "confidence": 0.8017578125, "bbox": [433.9970703125, 0.0, 953.4607421875, 521.2962890625]}, "conf_diff": 0.014460444450378418}], "onnx_only": [], "rknn_only": [], "differences": []}}]}