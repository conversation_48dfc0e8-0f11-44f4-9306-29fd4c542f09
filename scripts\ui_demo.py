#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI界面演示 - 不使用模型，展示界面效果
"""

import cv2
import numpy as np
import time
import argparse
import random
from collections import deque

# 类别名称映射
CLASS_NAMES = {
    0: 'apple', 1: 'banana', 2: 'board', 3: 'cake', 4: 'chili', 5: 'cola',
    6: 'greenlight', 7: 'milk', 8: 'potato', 9: 'redlight', 10: 'tomato', 11: 'watermelon'
}

# 类别颜色映射 (BGR格式)
CLASS_COLORS = {
    0: (0, 255, 100),    # apple - 青绿色
    1: (0, 200, 255),    # banana - 橙黄色
    2: (128, 128, 128),  # board - 灰色
    3: (255, 100, 255),  # cake - 紫红色
    4: (0, 100, 255),    # chili - 橙红色
    5: (100, 50, 200),   # cola - 深紫色
    6: (0, 255, 0),      # greenlight - 纯绿色
    7: (255, 255, 255),  # milk - 白色
    8: (0, 150, 255),    # potato - 橙色
    9: (0, 50, 255),     # redlight - 红色
    10: (0, 100, 255),   # tomato - 橙红色
    11: (100, 255, 100), # watermelon - 浅绿色
}

class SettingsPanel:
    def __init__(self, initial_conf=0.25, initial_nms=0.45):
        self.show_settings = False
        self.conf_threshold = initial_conf
        self.nms_threshold = initial_nms
        self.conf_slider_pos = int(initial_conf * 100)
        self.nms_slider_pos = int(initial_nms * 100)
        
    def draw_settings_button(self, frame):
        """绘制设置按钮"""
        h, w = frame.shape[:2]
        
        # 设置按钮位置（左上角）
        btn_x, btn_y = 20, 60
        btn_w, btn_h = 80, 30
        
        # 绘制按钮背景
        color = (0, 255, 255) if not self.show_settings else (0, 200, 200)
        overlay = frame.copy()
        cv2.rectangle(overlay, (btn_x, btn_y), (btn_x + btn_w, btn_y + btn_h), (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)
        
        # 绘制按钮边框和文字
        cv2.rectangle(frame, (btn_x, btn_y), (btn_x + btn_w, btn_y + btn_h), color, 2)
        cv2.putText(frame, "Settings", (btn_x + 8, btn_y + 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        return (btn_x, btn_y, btn_w, btn_h)
    
    def draw_settings_panel(self, frame):
        """绘制设置面板"""
        if not self.show_settings:
            return
            
        h, w = frame.shape[:2]
        
        # 设置面板位置和尺寸
        panel_x, panel_y = 20, 100
        panel_w, panel_h = 300, 150
        
        # 绘制面板背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (panel_x, panel_y), 
                     (panel_x + panel_w, panel_y + panel_h), (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.6, overlay, 0.4, 0, frame)
        
        # 绘制面板边框
        cv2.rectangle(frame, (panel_x, panel_y), 
                     (panel_x + panel_w, panel_y + panel_h), (0, 255, 255), 2)
        
        # 标题
        cv2.putText(frame, "Detection Settings", (panel_x + 10, panel_y + 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
        
        # 置信度滑块
        conf_y = panel_y + 50
        cv2.putText(frame, f"Confidence: {self.conf_threshold:.2f}", 
                   (panel_x + 10, conf_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 置信度滑块轨道
        slider_x = panel_x + 10
        slider_y = conf_y + 15
        slider_w = 250
        cv2.line(frame, (slider_x, slider_y), (slider_x + slider_w, slider_y), (100, 100, 100), 3)
        
        # 置信度滑块手柄
        handle_x = slider_x + int(self.conf_slider_pos * slider_w / 100)
        cv2.circle(frame, (handle_x, slider_y), 8, (0, 255, 255), -1)
        cv2.circle(frame, (handle_x, slider_y), 8, (255, 255, 255), 2)
        
        # NMS滑块
        nms_y = panel_y + 100
        cv2.putText(frame, f"NMS: {self.nms_threshold:.2f}", 
                   (panel_x + 10, nms_y), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # NMS滑块轨道
        nms_slider_y = nms_y + 15
        cv2.line(frame, (slider_x, nms_slider_y), (slider_x + slider_w, nms_slider_y), (100, 100, 100), 3)
        
        # NMS滑块手柄
        nms_handle_x = slider_x + int(self.nms_slider_pos * slider_w / 100)
        cv2.circle(frame, (nms_handle_x, nms_slider_y), 8, (0, 255, 255), -1)
        cv2.circle(frame, (nms_handle_x, nms_slider_y), 8, (255, 255, 255), 2)
        
        # 返回滑块区域信息
        return {
            'conf_slider': (slider_x, slider_y - 10, slider_w, 20),
            'nms_slider': (slider_x, nms_slider_y - 10, slider_w, 20)
        }
    
    def handle_mouse_click(self, x, y, button_area, slider_areas=None):
        """处理鼠标点击"""
        btn_x, btn_y, btn_w, btn_h = button_area
        
        # 检查是否点击设置按钮
        if btn_x <= x <= btn_x + btn_w and btn_y <= y <= btn_y + btn_h:
            self.show_settings = not self.show_settings
            return True
        
        # 如果设置面板打开，检查滑块点击
        if self.show_settings and slider_areas:
            # 置信度滑块
            if 'conf_slider' in slider_areas:
                sx, sy, sw, sh = slider_areas['conf_slider']
                if sx <= x <= sx + sw and sy <= y <= sy + sh:
                    self.conf_slider_pos = max(0, min(100, int((x - sx) * 100 / sw)))
                    self.conf_threshold = self.conf_slider_pos / 100.0
                    return True
            
            # NMS滑块
            if 'nms_slider' in slider_areas:
                sx, sy, sw, sh = slider_areas['nms_slider']
                if sx <= x <= sx + sw and sy <= y <= sy + sh:
                    self.nms_slider_pos = max(0, min(100, int((x - sx) * 100 / sw)))
                    self.nms_threshold = self.nms_slider_pos / 100.0
                    return True
        
        return False

class UIDemo:
    def __init__(self):
        self.settings = SettingsPanel(0.25, 0.45)
        self.fps_queue = deque(maxlen=30)
        self.inference_times = deque(maxlen=30)
        
        # 模拟检测结果
        self.mock_detections = []
        self.last_detection_update = 0
        
    def generate_mock_detections(self, frame_width, frame_height):
        """生成模拟检测结果"""
        current_time = time.time()
        
        # 每2秒更新一次检测结果
        if current_time - self.last_detection_update > 2.0:
            self.mock_detections = []
            
            # 随机生成1-3个检测框
            num_detections = random.randint(1, 3)
            
            for _ in range(num_detections):
                # 随机选择类别（排除board）
                class_id = random.choice([0, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11])
                
                # 随机生成边界框
                w = random.randint(80, 200)
                h = random.randint(80, 200)
                x1 = random.randint(50, frame_width - w - 50)
                y1 = random.randint(50, frame_height - h - 50)
                x2 = x1 + w
                y2 = y1 + h
                
                # 随机置信度
                confidence = random.uniform(0.3, 0.95)
                
                # 根据置信度阈值过滤
                if confidence >= self.settings.conf_threshold:
                    self.mock_detections.append({
                        'class_id': class_id,
                        'class_name': CLASS_NAMES[class_id],
                        'confidence': confidence,
                        'bbox': [x1, y1, x2, y2]
                    })
            
            self.last_detection_update = current_time
        
        return self.mock_detections
    
    def draw_corner_box(self, frame, x1, y1, x2, y2, color, thickness=2, corner_length=20):
        """绘制四角边线框"""
        # 左上角
        cv2.line(frame, (x1, y1), (x1 + corner_length, y1), color, thickness)
        cv2.line(frame, (x1, y1), (x1, y1 + corner_length), color, thickness)
        
        # 右上角
        cv2.line(frame, (x2, y1), (x2 - corner_length, y1), color, thickness)
        cv2.line(frame, (x2, y1), (x2, y1 + corner_length), color, thickness)
        
        # 左下角
        cv2.line(frame, (x1, y2), (x1 + corner_length, y2), color, thickness)
        cv2.line(frame, (x1, y2), (x1, y2 - corner_length), color, thickness)
        
        # 右下角
        cv2.line(frame, (x2, y2), (x2 - corner_length, y2), color, thickness)
        cv2.line(frame, (x2, y2), (x2, y2 - corner_length), color, thickness)
    
    def draw_detections(self, frame):
        """绘制检测结果"""
        annotated_frame = frame.copy()
        h, w = frame.shape[:2]
        
        # 生成模拟检测结果
        detections = self.generate_mock_detections(w, h)
        
        # 模拟推理时间
        inference_time = random.uniform(15, 35)
        self.inference_times.append(inference_time)
        
        # 绘制检测框
        for det in detections:
            x1, y1, x2, y2 = [int(coord) for coord in det['bbox']]
            class_id = det['class_id']
            class_name = det['class_name']
            confidence = det['confidence']
            
            # 获取颜色
            color = CLASS_COLORS.get(class_id, (0, 255, 255))
            
            # 绘制淡色蒙版
            mask_color = tuple(int(c * 0.3) for c in color)
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (x1, y1), (x2, y2), mask_color, -1)
            cv2.addWeighted(annotated_frame, 0.8, overlay, 0.2, 0, annotated_frame)
            
            # 绘制四角边线框
            corner_length = min(30, (x2-x1)//4, (y2-y1)//4)
            self.draw_corner_box(annotated_frame, x1, y1, x2, y2, color, 3, corner_length)
            
            # 绘制标签
            label = f"{class_name} {confidence:.2f}"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            font_thickness = 2
            
            # 计算文字尺寸
            (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, font_thickness)
            
            # 标签位置
            label_x = x1
            label_y = y1 - 10
            
            if label_y - text_height < 0:
                label_y = y1 + text_height + 10
            
            # 绘制标签背景
            bg_x1 = label_x - 5
            bg_y1 = label_y - text_height - 5
            bg_x2 = label_x + text_width + 5
            bg_y2 = label_y + 5
            
            overlay = annotated_frame.copy()
            cv2.rectangle(overlay, (bg_x1, bg_y1), (bg_x2, bg_y2), (0, 0, 0), -1)
            cv2.addWeighted(annotated_frame, 0.7, overlay, 0.3, 0, annotated_frame)
            
            cv2.rectangle(annotated_frame, (bg_x1, bg_y1), (bg_x2, bg_y2), color, 1)
            cv2.putText(annotated_frame, label, (label_x, label_y), font, font_scale, color, font_thickness)
        
        # 绘制性能信息
        self._draw_performance_info(annotated_frame, inference_time, len(detections))
        
        # 绘制设置按钮
        button_area = self.settings.draw_settings_button(annotated_frame)
        
        # 绘制设置面板
        slider_areas = self.settings.draw_settings_panel(annotated_frame)
        
        return annotated_frame, button_area, slider_areas, detections
    
    def _draw_performance_info(self, frame, inference_time, detection_count):
        """绘制性能信息"""
        h, w = frame.shape[:2]
        
        # 计算平均推理时间和FPS
        avg_inference_time = np.mean(self.inference_times) if self.inference_times else 0
        fps = 1000 / avg_inference_time if avg_inference_time > 0 else 0
        
        # FPS文本
        fps_text = f"FPS: {fps:.1f}"
        
        # 绘制FPS信息（右上角）
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        font_thickness = 2
        
        (text_width, text_height), baseline = cv2.getTextSize(fps_text, font, font_scale, font_thickness)
        
        fps_x = w - text_width - 20
        fps_y = 30
        
        # FPS背景
        overlay = frame.copy()
        cv2.rectangle(overlay, (fps_x - 10, fps_y - text_height - 5), 
                     (fps_x + text_width + 10, fps_y + 5), (0, 0, 0), -1)
        cv2.addWeighted(frame, 0.7, overlay, 0.3, 0, frame)
        
        cv2.rectangle(frame, (fps_x - 10, fps_y - text_height - 5), 
                     (fps_x + text_width + 10, fps_y + 5), (0, 255, 255), 1)
        cv2.putText(frame, fps_text, (fps_x, fps_y), font, font_scale, (0, 255, 255), font_thickness)
        
        # 检测数量
        if detection_count > 0:
            det_text = f"Objects: {detection_count}"
            det_font_scale = 0.5
            (det_width, det_height), _ = cv2.getTextSize(det_text, font, det_font_scale, 1)
            
            det_x = 20
            det_y = 30
            
            overlay = frame.copy()
            cv2.rectangle(overlay, (det_x - 5, det_y - det_height - 5), 
                         (det_x + det_width + 5, det_y + 5), (0, 0, 0), -1)
            cv2.addWeighted(frame, 0.8, overlay, 0.2, 0, frame)
            
            cv2.putText(frame, det_text, (det_x, det_y), font, det_font_scale, (100, 255, 100), 1)


def mouse_callback(event, x, y, flags, param):
    """鼠标回调函数"""
    ui_demo, button_area, slider_areas = param
    if event == cv2.EVENT_LBUTTONDOWN:
        ui_demo.settings.handle_mouse_click(x, y, button_area, slider_areas)


def main():
    parser = argparse.ArgumentParser(description='UI界面演示')
    parser.add_argument('--camera', type=int, default=0, help='摄像头设备ID')
    parser.add_argument('--width', type=int, default=1280, help='摄像头宽度')
    parser.add_argument('--height', type=int, default=720, help='摄像头高度')
    parser.add_argument('--fps', type=int, default=30, help='摄像头FPS')

    args = parser.parse_args()

    # 初始化UI演示
    ui_demo = UIDemo()

    # 初始化摄像头
    print(f"📷 初始化摄像头 (设备ID: {args.camera})...")
    cap = cv2.VideoCapture(args.camera)

    if not cap.isOpened():
        print(f"❌ 无法打开摄像头设备: {args.camera}")
        return

    # 设置摄像头参数
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.width)
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.height)
    cap.set(cv2.CAP_PROP_FPS, args.fps)

    # 获取实际摄像头参数
    actual_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    actual_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    actual_fps = cap.get(cv2.CAP_PROP_FPS)

    print(f"📷 摄像头参数: {actual_width}x{actual_height} @ {actual_fps:.1f}FPS")

    # 显示设置
    window_name = 'UI Demo - Real-time Detection'
    cv2.namedWindow(window_name, cv2.WINDOW_AUTOSIZE)
    print("🖥️  UI演示已启用")
    print("💡 按 'q' 退出，按 's' 截图，点击Settings调整参数")
    print("🎯 这是UI演示版本，显示模拟检测结果")
    print("="*60)

    frame_count = 0
    start_time = time.time()
    last_print_time = time.time()
    button_area = None
    slider_areas = None

    try:
        while True:
            # 读取帧
            ret, frame = cap.read()
            if not ret:
                print("❌ 无法读取摄像头帧")
                break

            frame_count += 1

            # 绘制UI和模拟检测结果
            annotated_frame, button_area, slider_areas, detections = ui_demo.draw_detections(frame)

            # 设置鼠标回调
            cv2.setMouseCallback(window_name, mouse_callback,
                               (ui_demo, button_area, slider_areas))

            # 显示画面
            cv2.imshow(window_name, annotated_frame)

            # 处理按键
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                print("🛑 用户退出")
                break
            elif key == ord('s'):
                # 截图
                screenshot_path = f"ui_demo_screenshot_{int(time.time())}.jpg"
                cv2.imwrite(screenshot_path, annotated_frame)
                print(f"📸 截图保存到: {screenshot_path}")

            # 打印状态信息（每5秒打印一次）
            current_time = time.time()
            if current_time - last_print_time >= 5.0:
                elapsed_time = current_time - start_time
                avg_fps = frame_count / elapsed_time if elapsed_time > 0 else 0
                avg_inference = np.mean(ui_demo.inference_times) if ui_demo.inference_times else 0

                print(f"📊 帧数: {frame_count}, 平均FPS: {avg_fps:.1f}, "
                      f"模拟推理时间: {avg_inference:.1f}ms, 当前检测: {len(detections)}")
                print(f"🎛️  当前设置: 置信度={ui_demo.settings.conf_threshold:.2f}, "
                      f"NMS={ui_demo.settings.nms_threshold:.2f}")

                if detections:
                    print("🎯 模拟检测结果:")
                    for det in detections:
                        print(f"   - {det['class_name']}: {det['confidence']:.3f}")

                last_print_time = current_time

    except KeyboardInterrupt:
        print("\n🛑 收到中断信号，正在退出...")

    except Exception as e:
        print(f"❌ 运行时错误: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # 清理资源
        print("🧹 清理资源...")

        if cap:
            cap.release()
            print("✅ 摄像头已释放")

        cv2.destroyAllWindows()
        print("✅ 显示窗口已关闭")

        # 最终统计信息
        if frame_count > 0:
            total_time = time.time() - start_time
            avg_fps = frame_count / total_time
            print(f"📊 总计处理 {frame_count} 帧")
            print(f"📊 平均FPS: {avg_fps:.1f}")


if __name__ == '__main__':
    main()
