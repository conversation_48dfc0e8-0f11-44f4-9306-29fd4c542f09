#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ONNX vs RKNN 模型输出比较脚本
用于分析量化精度损失对检测结果的影响
"""

import cv2
import numpy as np
import time
import argparse
from pathlib import Path
import onnxruntime as ort

# 类别映射（与data.yaml一致）
CLASS_NAMES = {
    0: 'apple', 1: 'banana', 2: 'board', 3: 'cake', 4: 'chili', 5: 'cola',
    6: 'greenlight', 7: 'milk', 8: 'potato', 9: 'redlight', 10: 'tomato', 11: 'watermelon'
}

CLASS_NAMES_CN = {
    0: '苹果', 1: '香蕉', 2: '板子', 3: '蛋糕', 4: '彩椒', 5: '可乐',
    6: '绿灯', 7: '牛奶', 8: '土豆', 9: '红灯', 10: '西红柿', 11: '西瓜'
}

def preprocess_image_onnx(image_path, img_size=640):
    """ONNX图像预处理"""
    try:
        img_array = np.fromfile(image_path, dtype=np.uint8)
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        if img is None:
            return None, None, None
    except Exception as e:
        print(f"❌ 读取图像失败: {e}")
        return None, None, None
    
    original_img = img.copy()
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    h, w = img_rgb.shape[:2]
    scale = min(img_size / h, img_size / w)
    new_h, new_w = int(h * scale), int(w * scale)
    
    img_resized = cv2.resize(img_rgb, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    padded_img = np.full((img_size, img_size, 3), 114, dtype=np.uint8)
    
    top = (img_size - new_h) // 2
    left = (img_size - new_w) // 2
    padded_img[top:top+new_h, left:left+new_w] = img_resized
    
    # ONNX: NCHW, float32, [0,1]
    padded_img = padded_img.astype(np.float32) / 255.0
    padded_img = np.transpose(padded_img, (2, 0, 1))
    padded_img = np.expand_dims(padded_img, axis=0)
    
    return padded_img, original_img, scale

def preprocess_image_rknn(image_path, img_size=640):
    """RKNN图像预处理"""
    try:
        img_array = np.fromfile(image_path, dtype=np.uint8)
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        if img is None:
            return None, None, None
    except Exception as e:
        print(f"❌ 读取图像失败: {e}")
        return None, None, None
    
    original_img = img.copy()
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    h, w = img_rgb.shape[:2]
    scale = min(img_size / h, img_size / w)
    new_h, new_w = int(h * scale), int(w * scale)
    
    img_resized = cv2.resize(img_rgb, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
    padded_img = np.full((img_size, img_size, 3), 114, dtype=np.uint8)
    
    top = (img_size - new_h) // 2
    left = (img_size - new_w) // 2
    padded_img[top:top+new_h, left:left+new_w] = img_resized
    
    # RKNN: NHWC, uint8, [0,255]
    padded_img = np.expand_dims(padded_img, axis=0)
    
    return padded_img, original_img, scale

def analyze_output(output, model_type):
    """分析模型输出"""
    print(f"\n=== {model_type} 模型输出分析 ===")
    print(f"输出形状: {output.shape}")
    print(f"数据类型: {output.dtype}")
    print(f"数值范围: [{output.min():.3f}, {output.max():.3f}]")
    
    # 处理输出格式
    if len(output.shape) == 3:
        output = output[0]
    
    if output.shape[0] == 16:
        output = output.T
    
    # 提取边界框和类别概率
    boxes = output[:, :4]
    scores = output[:, 4:]
    
    # 获取最大类别概率和索引
    max_scores = np.max(scores, axis=1)
    class_ids = np.argmax(scores, axis=1)
    
    # 找到置信度最高的检测
    best_idx = np.argmax(max_scores)
    best_conf = max_scores[best_idx]
    best_class = class_ids[best_idx]
    
    print(f"最高置信度: {best_conf:.3f}")
    print(f"预测类别: {best_class} ({CLASS_NAMES.get(best_class, 'unknown')} - {CLASS_NAMES_CN.get(best_class, '未知')})")
    
    # 显示前5个最高置信度的检测
    top5_indices = np.argsort(max_scores)[-5:][::-1]
    print(f"前5个检测结果:")
    for i, idx in enumerate(top5_indices):
        conf = max_scores[idx]
        cls_id = class_ids[idx]
        if conf > 0.1:  # 只显示置信度>0.1的
            print(f"  {i+1}. 类别{cls_id}({CLASS_NAMES_CN.get(cls_id, '未知')}): {conf:.3f}")
    
    return best_class, best_conf

def main():
    parser = argparse.ArgumentParser(description='ONNX vs RKNN 模型输出比较')
    parser.add_argument('--onnx', type=str, default='converted_models/best.onnx', help='ONNX模型路径')
    parser.add_argument('--rknn', type=str, help='RKNN模型路径（可选，仅在RK3588上可用）')
    parser.add_argument('--image', type=str, required=True, help='测试图像路径')
    
    args = parser.parse_args()
    
    print(f"🔍 分析图像: {args.image}")
    
    # 测试ONNX模型
    print("\n" + "="*60)
    print("测试ONNX模型")
    print("="*60)
    
    if not Path(args.onnx).exists():
        print(f"❌ ONNX模型不存在: {args.onnx}")
        return
    
    # 加载ONNX模型
    session = ort.InferenceSession(args.onnx)
    input_name = session.get_inputs()[0].name
    output_name = session.get_outputs()[0].name
    
    # ONNX推理
    onnx_input, _, _ = preprocess_image_onnx(args.image)
    if onnx_input is None:
        print("❌ ONNX图像预处理失败")
        return
    
    onnx_outputs = session.run([output_name], {input_name: onnx_input})
    onnx_class, onnx_conf = analyze_output(onnx_outputs[0], "ONNX")
    
    # 测试RKNN模型（如果提供且在RK3588上）
    if args.rknn:
        print("\n" + "="*60)
        print("测试RKNN模型")
        print("="*60)
        
        try:
            from rknn.api import RKNN
            
            if not Path(args.rknn).exists():
                print(f"❌ RKNN模型不存在: {args.rknn}")
                return
            
            # 加载RKNN模型
            rknn = RKNN(verbose=False)
            ret = rknn.load_rknn(args.rknn)
            if ret != 0:
                print(f"❌ 加载RKNN模型失败")
                return
            
            ret = rknn.init_runtime(target='rk3588')
            if ret != 0:
                print("❌ 初始化RKNN运行时失败")
                return
            
            # RKNN推理
            rknn_input, _, _ = preprocess_image_rknn(args.image)
            if rknn_input is None:
                print("❌ RKNN图像预处理失败")
                return
            
            rknn_outputs = rknn.inference(inputs=[rknn_input], data_format=['nhwc'])
            rknn_class, rknn_conf = analyze_output(rknn_outputs[0], "RKNN")
            
            # 比较结果
            print("\n" + "="*60)
            print("结果比较")
            print("="*60)
            print(f"ONNX预测: {CLASS_NAMES_CN.get(onnx_class, '未知')} (置信度: {onnx_conf:.3f})")
            print(f"RKNN预测: {CLASS_NAMES_CN.get(rknn_class, '未知')} (置信度: {rknn_conf:.3f})")
            
            if onnx_class == rknn_class:
                print("✅ 预测结果一致")
            else:
                print("❌ 预测结果不一致 - 存在量化精度损失")
                print(f"类别差异: ONNX={onnx_class}, RKNN={rknn_class}")
            
            rknn.release()
            
        except ImportError:
            print("❌ RKNN-Toolkit未安装，跳过RKNN测试")
        except Exception as e:
            print(f"❌ RKNN测试失败: {e}")
    
    else:
        print("\n💡 提示: 使用 --rknn 参数指定RKNN模型路径以进行比较")

if __name__ == '__main__':
    main()
