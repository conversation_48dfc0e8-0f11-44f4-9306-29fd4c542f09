#!/usr/bin/env python3
"""
YOLO物体识别系统主脚本
集成训练、预测、评估功能
支持识别：苹果，香蕉，西瓜，土豆，西红柿，彩椒，牛奶，可乐，蛋糕，红灯，绿灯
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from scripts.train import YOLOTrainer
from scripts.predict import YOLOPredictor
from scripts.evaluate import YOLOEvaluator

def print_banner():
    """打印系统横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    YOLO物体识别系统                          ║
    ║                                                              ║
    ║  支持识别类别：                                              ║
    ║  🍎 苹果    🍌 香蕉    🍉 西瓜    🥔 土豆    🍅 西红柿      ║
    ║  🌶️ 彩椒    🥛 牛奶    🥤 可乐    🍰 蛋糕                   ║
    ║  🔴 红灯    🟢 绿灯                                          ║
    ║                                                              ║
    ║  功能：训练、预测、评估                                      ║
    ║  准确率：≥90%                                            ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def train_model(args):
    """训练模型"""
    print("\n🚀 开始训练模型...")
    
    trainer = YOLOTrainer(
        config_path=args.config,
        model_path=args.pretrained_model
    )
    
    if args.resume:
        results = trainer.resume_training(args.resume, args.epochs)
    else:
        results, val_results = trainer.train(
            epochs=args.epochs,
            batch_size=args.batch_size,
            img_size=args.img_size,
            patience=args.patience,
            project_name=args.project_name
        )
    
    print("✅ 训练完成!")
    return results

def predict_with_model(args):
    """使用模型进行预测"""
    print("\n🔍 开始预测...")
    
    predictor = YOLOPredictor(
        model_path=args.model,
        conf_threshold=args.conf,
        iou_threshold=args.iou,
        filter_board=not getattr(args, 'show_board', False)  # 默认过滤board类别
    )
    
    show_chinese = not args.english
    
    if args.camera:
        # 摄像头预测
        camera_id = int(args.source) if args.source else 0
        predictor.predict_camera(camera_id, show_chinese)
    elif args.batch:
        # 批量预测
        if not args.output:
            args.output = "runs/predict/batch_results"
        predictor.predict_batch(args.source, args.output, show_chinese)
    else:
        # 单张图片预测
        results, annotated_image = predictor.predict_image(
            args.source, 
            args.output,
            show_chinese
        )
        
        # 如果没有指定输出路径，显示结果
        if not args.output:
            import cv2
            cv2.imshow('预测结果', annotated_image)
            cv2.waitKey(0)
            cv2.destroyAllWindows()
    
    print("✅ 预测完成!")

def evaluate_model(args):
    """评估模型"""
    print("\n📊 开始评估模型...")
    
    evaluator = YOLOEvaluator(
        model_path=args.model,
        config_path=args.config
    )
    
    # 评估模型
    results = evaluator.evaluate(save_dir=args.save_dir)
    
    # 场地测试
    if args.field_test:
        print("\n🏟️ 开始场地图片测试...")
        evaluator.test_on_field_images(args.field_test)
    
    print("✅ 评估完成!")
    return results

def quick_start():
    """快速开始指南"""
    print("\n📖 快速开始指南:")
    print("=" * 60)
    print("1. 训练模型:")
    print("   python scripts/main.py train --epochs 100 --batch-size 16")
    print()
    print("2. 预测单张图片:")
    print("   python scripts/main.py predict --model runs/train/xxx/weights/best.pt --source image.jpg")
    print()
    print("3. 批量预测:")
    print("   python scripts/main.py predict --model runs/train/xxx/weights/best.pt --source 场地图片 --batch --output results")
    print()
    print("4. 摄像头实时预测:")
    print("   python scripts/main.py predict --model runs/train/xxx/weights/best.pt --camera")
    print()
    print("5. 评估模型:")
    print("   python scripts/main.py evaluate --model runs/train/xxx/weights/best.pt")
    print()
    print("6. 场地测试:")
    print("   python scripts/main.py evaluate --model runs/train/xxx/weights/best.pt --field-test 场地图片")
    print()

def main():
    parser = argparse.ArgumentParser(description='YOLO物体识别系统')
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 训练命令
    train_parser = subparsers.add_parser('train', help='训练模型')
    train_parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    train_parser.add_argument('--batch-size', type=int, default=16, help='批次大小')
    train_parser.add_argument('--img-size', type=int, default=640, help='输入图像尺寸')
    train_parser.add_argument('--patience', type=int, default=50, help='早停耐心值')
    train_parser.add_argument('--project-name', type=str, help='项目名称')
    train_parser.add_argument('--resume', type=str, help='从检查点恢复训练')
    train_parser.add_argument('--config', type=str, default='data/data.yaml', help='数据配置文件')
    train_parser.add_argument('--pretrained-model', type=str, default='scripts/model/yolo11n.pt', help='预训练模型')
    
    # 预测命令
    predict_parser = subparsers.add_parser('predict', help='预测/推理')
    predict_parser.add_argument('--model', type=str, required=True, help='训练好的模型路径')
    predict_parser.add_argument('--source', type=str, help='输入源：图片路径、目录路径或摄像头ID')
    predict_parser.add_argument('--output', type=str, help='输出路径')
    predict_parser.add_argument('--conf', type=float, default=0.5, help='置信度阈值')
    predict_parser.add_argument('--iou', type=float, default=0.45, help='IoU阈值')
    predict_parser.add_argument('--camera', action='store_true', help='使用摄像头')
    predict_parser.add_argument('--batch', action='store_true', help='批量处理目录')
    predict_parser.add_argument('--english', action='store_true', help='使用英文标签')
    predict_parser.add_argument('--show-board', action='store_true', help='显示board类别检测结果')
    
    # 评估命令
    evaluate_parser = subparsers.add_parser('evaluate', help='评估模型')
    evaluate_parser.add_argument('--model', type=str, required=True, help='训练好的模型路径')
    evaluate_parser.add_argument('--config', type=str, default='data/data.yaml', help='数据配置文件')
    evaluate_parser.add_argument('--save-dir', type=str, help='保存评估结果的目录')
    evaluate_parser.add_argument('--field-test', type=str, help='场地图片目录，用于实际场景测试')
    
    # 帮助命令
    help_parser = subparsers.add_parser('help', help='显示帮助信息')
    
    args = parser.parse_args()
    
    # 打印横幅
    print_banner()
    
    if args.command == 'train':
        train_model(args)
    elif args.command == 'predict':
        predict_with_model(args)
    elif args.command == 'evaluate':
        evaluate_model(args)
    elif args.command == 'help' or args.command is None:
        quick_start()
    else:
        print(f"未知命令: {args.command}")
        quick_start()

if __name__ == "__main__":
    main()
