#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
准备RKNN量化数据集
从训练数据中采样图片用于量化校准
"""

import os
import cv2
import numpy as np
import argparse
import random
from pathlib import Path
import yaml

def load_dataset_config(data_yaml_path):
    """加载数据集配置"""
    with open(data_yaml_path, 'r', encoding='utf-8') as f:
        data_config = yaml.safe_load(f)
    return data_config

def preprocess_image(image_path, img_size=640):
    """
    预处理图像用于量化
    
    Args:
        image_path: 图像路径
        img_size: 目标尺寸
    
    Returns:
        preprocessed_image: 预处理后的图像 (1, 3, H, W)
    """
    # 读取图像
    img = cv2.imread(image_path)
    if img is None:
        return None
    
    # 转换颜色空间 - 保持RGB格式与ONNX训练时一致
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

    # 调整尺寸 (保持宽高比)
    h, w = img.shape[:2]
    scale = min(img_size / h, img_size / w)
    new_h, new_w = int(h * scale), int(w * scale)

    # 缩放图像
    img = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LINEAR)

    # 创建填充图像
    padded_img = np.full((img_size, img_size, 3), 114, dtype=np.uint8)

    # 计算填充位置
    top = (img_size - new_h) // 2
    left = (img_size - new_w) // 2

    # 放置图像
    padded_img[top:top+new_h, left:left+new_w] = img

    # 注意：RKNN量化需要uint8格式的NHWC数据，不需要归一化
    # RKNN内部会根据mean_values和std_values进行归一化
    # 保持uint8格式 [0, 255]，NHWC布局

    # 添加批次维度 (NHWC格式)
    padded_img = np.expand_dims(padded_img, axis=0)
    
    return padded_img

def collect_images(data_yaml_path, num_samples=100, img_size=640):
    """
    从数据集中收集图像用于量化
    
    Args:
        data_yaml_path: 数据集配置文件路径
        num_samples: 采样数量
        img_size: 图像尺寸
    
    Returns:
        images: 预处理后的图像列表
    """
    print(f"从数据集收集量化图像...")
    print(f"数据配置: {data_yaml_path}")
    print(f"采样数量: {num_samples}")
    print(f"图像尺寸: {img_size}")
    
    # 加载数据集配置
    data_config = load_dataset_config(data_yaml_path)
    
    # 获取训练图像路径
    train_path = data_config.get('train', '')
    if not os.path.isabs(train_path):
        # 相对路径，相对于yaml文件的目录
        yaml_dir = os.path.dirname(data_yaml_path)
        train_path = os.path.join(yaml_dir, train_path)
    
    print(f"训练数据路径: {train_path}")
    
    # 收集所有图像文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.bmp']
    all_images = []
    
    if os.path.exists(train_path):
        for ext in image_extensions:
            all_images.extend(Path(train_path).rglob(f'*{ext}'))
            all_images.extend(Path(train_path).rglob(f'*{ext.upper()}'))
    
    print(f"找到 {len(all_images)} 张训练图像")
    
    if len(all_images) == 0:
        print("未找到训练图像")
        return []
    
    # 随机采样
    sample_size = min(num_samples, len(all_images))
    sampled_images = random.sample(all_images, sample_size)
    
    print(f"采样 {sample_size} 张图像用于量化")
    
    # 预处理图像
    processed_images = []
    for i, img_path in enumerate(sampled_images):
        print(f"处理图像 ({i+1}/{sample_size}): {img_path.name}")
        
        processed_img = preprocess_image(str(img_path), img_size)
        if processed_img is not None:
            processed_images.append(processed_img)
        else:
            print(f"跳过无效图像: {img_path}")

    print(f"成功处理 {len(processed_images)} 张图像")
    return processed_images

def save_quantization_dataset(images, output_path):
    """
    保存量化数据集
    
    Args:
        images: 预处理后的图像列表
        output_path: 输出路径
    """
    print(f"保存量化数据集到: {output_path}")
    
    # 创建输出目录
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    # 合并所有图像
    if images:
        dataset = np.concatenate(images, axis=0)
        print(f"数据集形状: {dataset.shape}")
        
        # 保存为numpy文件
        np.save(output_path, dataset)
        print(f"量化数据集保存成功")

        return True
    else:
        print("没有有效的图像数据")
        return False

def main():
    parser = argparse.ArgumentParser(description='Prepare quantization dataset')
    parser.add_argument('--data-yaml', type=str, default='data/data.yaml', 
                       help='Path to dataset YAML file')
    parser.add_argument('--output', type=str, default='quantization_data/calibration_dataset.npy',
                       help='Output path for quantization dataset')
    parser.add_argument('--num-samples', type=int, default=100,
                       help='Number of samples for quantization')
    parser.add_argument('--img-size', type=int, default=640,
                       help='Input image size')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed for reproducibility')
    
    args = parser.parse_args()
    
    # 设置随机种子
    random.seed(args.seed)
    np.random.seed(args.seed)
    
    # 检查数据配置文件
    if not os.path.exists(args.data_yaml):
        print(f"❌ 数据配置文件不存在: {args.data_yaml}")
        return
    
    # 收集图像
    images = collect_images(
        data_yaml_path=args.data_yaml,
        num_samples=args.num_samples,
        img_size=args.img_size
    )
    
    # 保存数据集
    if images:
        success = save_quantization_dataset(images, args.output)
        if success:
            print(f"🎉 量化数据集准备完成!")
            print(f"📁 数据集路径: {args.output}")
            print(f"📊 数据集大小: {len(images)} 张图像")
        else:
            print("💥 量化数据集保存失败!")
    else:
        print("💥 量化数据集准备失败!")

if __name__ == '__main__':
    main()
